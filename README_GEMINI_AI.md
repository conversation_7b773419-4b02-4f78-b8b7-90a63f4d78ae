# 🤖 Gemini AI CLI - Standalone AI Assistant

A powerful command-line AI assistant similar to Google's Gemini, powered by DeepSeek via OpenRouter.

## 🚀 Quick Start

### 1. Set Up API Key (Required)
```bash
python setup_api_key.py
```

### 2. Run the AI Assistant
```bash
python gemini_ai_cli.py
```

## 🔑 API Key Setup

The AI assistant requires an OpenRouter API key to function. The original embedded key has expired for security reasons.

### Get Your Free API Key
1. Visit [https://openrouter.ai/](https://openrouter.ai/)
2. Sign up for a free account
3. Go to your dashboard
4. Generate an API key (starts with `sk-or-v1-`)

### Configure the API Key

**Option 1 - Automatic Setup (Recommended):**
```bash
python setup_api_key.py
```

**Option 2 - Manual Setup:**

**Windows:**
```cmd
set OPENROUTER_API_KEY=your-api-key-here
```

**macOS/Linux:**
```bash
export OPENROUTER_API_KEY='your-api-key-here'
```

**Config File (Any Platform):**
```bash
echo 'your-api-key-here' > ~/.openrouter_key
```

## ❌ Fixing "No auth credentials found" Error

If you see this error:
```
❌ Error: HTTP Error 401: {"error":{"message":"No auth credentials found","code":401}}
```

**Quick Fix:**
1. Run `python setup_api_key.py`
2. Enter your OpenRouter API key
3. Choose setup method
4. Test the connection

**Detailed Help:** See [FIX_API_ERROR.md](FIX_API_ERROR.md)

## 🎮 Usage

### Interactive Mode
```bash
python gemini_ai_cli.py
```

Then chat with the AI:
```
💬 You: Hello, how are you?
🤖 AI: Hello! I'm doing well, thank you for asking...

💬 You: Write a Python function to calculate fibonacci numbers
🤖 AI: Here's a Python function to calculate Fibonacci numbers:
...

💬 You: exit
👋 Goodbye!
```

### Available Commands
- Type any question or prompt
- `help` - Show help information
- `clear` - Clear the screen
- `exit` or `quit` - Exit the application

## ✨ Features

- 🤖 **AI-Powered Conversations**: Chat with DeepSeek AI model
- 🎨 **Beautiful Interface**: Rich CLI with colors and animations
- 💻 **Cross-Platform**: Works on Windows, macOS, and Linux
- 🔒 **Secure**: Your API key stays private
- ⚡ **Fast**: Quick responses and minimal resource usage
- 🛠 **Easy Setup**: Automated API key configuration

## 📋 Requirements

- **Python 3.8+**
- **Internet connection**
- **OpenRouter API key** (free)

## 🔧 Installation

### Install Dependencies
```bash
pip install requests rich
```

### Set Up API Key
```bash
python setup_api_key.py
```

### Run the Application
```bash
python gemini_ai_cli.py
```

## 🆘 Troubleshooting

### Common Issues

**"No auth credentials found"**
- Run `python setup_api_key.py`
- Make sure your API key is valid

**"Python not found"**
- Install Python from [python.org](https://python.org)
- Make sure Python is in your PATH

**"Module not found"**
- Install dependencies: `pip install requests rich`

**API key not persisting**
- Use the config file method: `echo 'your-key' > ~/.openrouter_key`
- Or set permanent environment variable

### Get Help
1. Check [FIX_API_ERROR.md](FIX_API_ERROR.md) for detailed solutions
2. Run `python setup_api_key.py` for interactive setup
3. Verify your OpenRouter account and API key

## 🌟 Why This AI Assistant?

- ✅ **Free to use** with OpenRouter's free tier
- ✅ **Privacy-focused** - your conversations aren't stored
- ✅ **Offline-capable** - runs locally on your machine
- ✅ **Customizable** - modify the code to suit your needs
- ✅ **No subscriptions** - just pay for what you use (if anything)

## 📖 Files in This Project

- `gemini_ai_cli.py` - Main AI assistant application
- `setup_api_key.py` - Interactive API key setup tool
- `test_api.py` - API connection test utility
- `FIX_API_ERROR.md` - Detailed troubleshooting guide
- `API_KEY_SETUP.md` - Complete API key setup guide

## 🚀 Ready to Chat with AI?

1. **Get your API key**: [openrouter.ai](https://openrouter.ai/)
2. **Run setup**: `python setup_api_key.py`
3. **Start chatting**: `python gemini_ai_cli.py`

Enjoy your personal AI assistant! 🎉
