#!/usr/bin/env python3
"""
Simple test script to verify OpenRouter API connectivity
"""

import json
import sys

try:
    import requests
except ImportError:
    print("❌ Error: 'requests' library not found.")
    print("Please install it with: pip install requests")
    sys.exit(1)

# Configuration
OPENROUTER_URL = "https://openrouter.ai/api/v1/chat/completions"
API_KEY = "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"
MODEL = "deepseek/deepseek-chat-v3-0324:free"

def test_api():
    """Test the OpenRouter API connection"""
    print("🧪 Testing OpenRouter API connection...")
    print("=====================================")
    
    # Create test payload
    payload = {
        "model": MODEL,
        "messages": [
            {
                "role": "user",
                "content": "Hello! Please respond with 'API test successful' if you can read this."
            }
        ]
    }
    
    headers = {
        'Content-Type': 'application/json',
        'Authorization': f'Bearer {API_KEY}'
    }
    
    try:
        print("📡 Sending test request...")
        
        response = requests.post(
            OPENROUTER_URL, 
            json=payload, 
            headers=headers, 
            timeout=30
        )
        
        print(f"📊 HTTP Status: {response.status_code}")
        
        if response.status_code != 200:
            print(f"❌ API request failed with status {response.status_code}")
            print(f"Response: {response.text}")
            return False
        
        data = response.json()
        
        # Check for API errors
        if 'error' in data:
            print(f"❌ API error: {data['error'].get('message', 'Unknown error')}")
            return False
        
        # Check response
        if 'choices' not in data or len(data['choices']) == 0:
            print("❌ No response choices received")
            return False
        
        ai_response = data['choices'][0]['message']['content']
        print(f"✅ AI Response: {ai_response}")
        print("\n🎉 API test completed successfully!")
        print("   The main application should work correctly.")
        return True
        
    except requests.exceptions.Timeout:
        print("❌ Request timed out. Please check your internet connection.")
        return False
    except requests.exceptions.ConnectionError:
        print("❌ Connection error. Please check your internet connection.")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

if __name__ == "__main__":
    success = test_api()
    sys.exit(0 if success else 1)
