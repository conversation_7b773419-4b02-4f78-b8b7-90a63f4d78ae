#!/bin/bash

# ============================================================================
# Craft Code CLI - Unix/Linux/macOS Installer
# Automatically installs Python (if needed) and sets up global codecraft command
# ============================================================================

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Print colored output
print_header() {
    echo -e "${CYAN}============================================================================${NC}"
    echo -e "${CYAN}🚀 Craft Code CLI - Unix/Linux/macOS Installer${NC}"
    echo -e "${CYAN}   by CodeCraft Team${NC}"
    echo -e "${CYAN}============================================================================${NC}"
    echo
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Get the directory where this script is located
INSTALL_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

print_header

echo -e "${BLUE}📁 Installation directory: ${INSTALL_DIR}${NC}"
echo

# ============================================================================
# Step 1: Check for Python installation
# ============================================================================

echo "[1/5] 🔍 Checking for Python installation..."

if command -v python3 &> /dev/null; then
    PYTHON_VERSION=$(python3 --version 2>&1 | cut -d' ' -f2)
    PYTHON_MAJOR=$(echo $PYTHON_VERSION | cut -d'.' -f1)
    PYTHON_MINOR=$(echo $PYTHON_VERSION | cut -d'.' -f2)
    
    if [ "$PYTHON_MAJOR" -ge 3 ] && [ "$PYTHON_MINOR" -ge 8 ]; then
        print_success "Python $PYTHON_VERSION found"
        PYTHON_INSTALLED=1
    else
        print_error "Python $PYTHON_VERSION found, but 3.8+ required"
        PYTHON_INSTALLED=0
    fi
elif command -v python &> /dev/null; then
    PYTHON_VERSION=$(python --version 2>&1 | cut -d' ' -f2)
    PYTHON_MAJOR=$(echo $PYTHON_VERSION | cut -d'.' -f1)
    
    if [ "$PYTHON_MAJOR" -ge 3 ]; then
        print_success "Python $PYTHON_VERSION found"
        PYTHON_INSTALLED=1
    else
        print_error "Python $PYTHON_VERSION found, but Python 3.8+ required"
        PYTHON_INSTALLED=0
    fi
else
    print_error "Python is not installed"
    PYTHON_INSTALLED=0
fi

# ============================================================================
# Step 2: Install Python if needed
# ============================================================================

if [ $PYTHON_INSTALLED -eq 0 ]; then
    echo
    echo "[2/5] 📥 Installing Python..."
    
    # Detect OS
    if [[ "$OSTYPE" == "linux-gnu"* ]]; then
        # Linux
        echo "🐧 Detected Linux system"
        
        if command -v apt &> /dev/null; then
            echo "📦 Using apt package manager..."
            sudo apt update
            sudo apt install -y python3 python3-pip python3-venv
        elif command -v yum &> /dev/null; then
            echo "📦 Using yum package manager..."
            sudo yum update
            sudo yum install -y python3 python3-pip
        elif command -v dnf &> /dev/null; then
            echo "📦 Using dnf package manager..."
            sudo dnf update
            sudo dnf install -y python3 python3-pip
        elif command -v pacman &> /dev/null; then
            echo "📦 Using pacman package manager..."
            sudo pacman -Sy
            sudo pacman -S --noconfirm python python-pip
        else
            print_error "Could not detect package manager"
            print_info "Please install Python 3.8+ manually and run this script again"
            exit 1
        fi
        
    elif [[ "$OSTYPE" == "darwin"* ]]; then
        # macOS
        echo "🍎 Detected macOS system"
        
        if command -v brew &> /dev/null; then
            echo "📦 Using Homebrew..."
            brew install python@3.11
        else
            print_error "Homebrew not found"
            print_info "Please install Homebrew first: https://brew.sh/"
            print_info "Then run: brew install python@3.11"
            exit 1
        fi
        
    else
        print_error "Unsupported operating system: $OSTYPE"
        print_info "Please install Python 3.8+ manually"
        exit 1
    fi
    
    # Verify Python installation
    if command -v python3 &> /dev/null; then
        PYTHON_VERSION=$(python3 --version 2>&1 | cut -d' ' -f2)
        print_success "Python $PYTHON_VERSION installed successfully"
    else
        print_error "Python installation failed"
        exit 1
    fi
else
    echo "[2/5] ⏭️  Python already installed, skipping..."
fi

# ============================================================================
# Step 3: Install dependencies
# ============================================================================

echo
echo "[3/5] 📦 Installing dependencies..."

# Determine Python command
if command -v python3 &> /dev/null; then
    PYTHON_CMD="python3"
elif command -v python &> /dev/null; then
    PYTHON_CMD="python"
else
    print_error "Python command not found"
    exit 1
fi

# Upgrade pip
echo "🔄 Upgrading pip..."
$PYTHON_CMD -m pip install --upgrade pip --user

# Install required packages
echo "📚 Installing required packages..."
$PYTHON_CMD -m pip install rich --user

if [ $? -eq 0 ]; then
    print_success "Dependencies installed successfully"
else
    print_error "Failed to install dependencies"
    exit 1
fi

# ============================================================================
# Step 4: Create global command
# ============================================================================

echo
echo "[4/5] 🌐 Creating global 'codecraft' command..."

# Determine installation directory for the script
LOCAL_BIN="$HOME/.local/bin"
GLOBAL_BIN="/usr/local/bin"

# Create ~/.local/bin if it doesn't exist
mkdir -p "$LOCAL_BIN"

# Create the codecraft script
CODECRAFT_SCRIPT="$LOCAL_BIN/codecraft"

cat > "$CODECRAFT_SCRIPT" << EOF
#!/bin/bash
$PYTHON_CMD "$INSTALL_DIR/src/main.py" "\$@"
EOF

# Make it executable
chmod +x "$CODECRAFT_SCRIPT"

if [ -f "$CODECRAFT_SCRIPT" ]; then
    print_success "Global 'codecraft' command created at $CODECRAFT_SCRIPT"
else
    print_error "Failed to create global command"
    exit 1
fi

# ============================================================================
# Step 5: Update PATH
# ============================================================================

echo
echo "[5/5] 🔍 Updating PATH configuration..."

# Add ~/.local/bin to PATH if not already there
if [[ ":$PATH:" != *":$LOCAL_BIN:"* ]]; then
    echo "📝 Adding $LOCAL_BIN to PATH..."
    
    # Determine shell and update appropriate config file
    SHELL_NAME=$(basename "$SHELL")
    
    case $SHELL_NAME in
        bash)
            PROFILE_FILE="$HOME/.bashrc"
            ;;
        zsh)
            PROFILE_FILE="$HOME/.zshrc"
            ;;
        fish)
            PROFILE_FILE="$HOME/.config/fish/config.fish"
            ;;
        *)
            PROFILE_FILE="$HOME/.profile"
            ;;
    esac
    
    # Add PATH export to profile
    if [ -f "$PROFILE_FILE" ]; then
        if ! grep -q "$LOCAL_BIN" "$PROFILE_FILE"; then
            echo "" >> "$PROFILE_FILE"
            echo "# Added by Craft Code CLI installer" >> "$PROFILE_FILE"
            echo "export PATH=\"\$PATH:$LOCAL_BIN\"" >> "$PROFILE_FILE"
            print_success "Added $LOCAL_BIN to PATH in $PROFILE_FILE"
        else
            print_info "$LOCAL_BIN already in PATH"
        fi
    else
        print_warning "Could not find shell profile file"
        print_info "Please add $LOCAL_BIN to your PATH manually"
    fi
    
    # Update PATH for current session
    export PATH="$PATH:$LOCAL_BIN"
else
    print_success "$LOCAL_BIN is already in PATH"
fi

# ============================================================================
# Test installation
# ============================================================================

echo
echo "🧪 Testing installation..."

# Test the codecraft command
if command -v codecraft &> /dev/null; then
    if codecraft --version &> /dev/null; then
        print_success "Installation test passed"
    else
        print_warning "Command exists but may not work correctly"
    fi
else
    print_warning "Command not found in PATH - you may need to restart your terminal"
fi

# ============================================================================
# Installation Complete
# ============================================================================

echo
echo -e "${CYAN}============================================================================${NC}"
echo -e "${CYAN}🎉 Installation Complete!${NC}"
echo -e "${CYAN}============================================================================${NC}"
echo
print_success "Craft Code CLI has been installed successfully!"
echo
echo -e "${BLUE}🚀 Usage:${NC}"
echo "   codecraft              # Launch interactive mode"
echo "   codecraft --help       # Show help"
echo "   codecraft --version    # Show version"
echo
echo -e "${BLUE}📁 Installation location:${NC} $INSTALL_DIR"
echo -e "${BLUE}🌐 Global command:${NC} codecraft"
echo
echo -e "${YELLOW}💡 If the 'codecraft' command doesn't work immediately:${NC}"
echo "   1. Restart your terminal"
echo "   2. Or run: source $PROFILE_FILE"
echo "   3. Or run directly: $PYTHON_CMD $INSTALL_DIR/src/main.py"
echo
echo -e "${BLUE}📖 For more information, visit:${NC}"
echo "   https://github.com/CodeCraft/craft-code-cli"
echo

print_success "Installation completed successfully!"
