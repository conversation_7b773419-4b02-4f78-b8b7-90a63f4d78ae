#!/usr/bin/env python3
"""
Craft Code CLI - Main Entry Point
A powerful command-line interface tool for developers
"""

import sys
import os
import argparse
from pathlib import Path

# Add src directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from cli import Craft<PERSON>odeCL<PERSON>
    from __init__ import __version__
except ImportError:
    # Fallback for development
    from src.cli import CraftCodeCL<PERSON>
    from src import __version__

def main():
    """Main entry point for Craft Code CLI"""
    
    # Create argument parser
    parser = argparse.ArgumentParser(
        prog='codecraft',
        description='🚀 Craft Code CLI - A powerful tool for developers',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  codecraft                    # Launch interactive mode
  codecraft create project     # Create a new project
  codecraft generate           # Generate code templates
  codecraft --version          # Show version information
  
For more information, visit: https://github.com/CodeCraft/craft-code-cli
        """
    )
    
    # Add version argument
    parser.add_argument(
        '--version', '-v',
        action='version',
        version=f'Craft Code CLI v{__version__}'
    )
    
    # Add subcommands
    subparsers = parser.add_subparsers(dest='command', help='Available commands')
    
    # Create command
    create_parser = subparsers.add_parser('create', help='Create new projects or files')
    create_parser.add_argument('type', choices=['project', 'file', 'template'], help='What to create')
    create_parser.add_argument('name', nargs='?', help='Name of the item to create')
    
    # Generate command
    generate_parser = subparsers.add_parser('generate', help='Generate code templates')
    generate_parser.add_argument('template', nargs='?', help='Template to generate')
    
    # Config command
    config_parser = subparsers.add_parser('config', help='Configure Craft Code CLI')
    config_parser.add_argument('action', choices=['set', 'get', 'list'], help='Configuration action')
    config_parser.add_argument('key', nargs='?', help='Configuration key')
    config_parser.add_argument('value', nargs='?', help='Configuration value')
    
    # Parse arguments
    args = parser.parse_args()
    
    # Initialize CLI
    cli = CraftCodeCLI()
    
    try:
        # Handle commands
        if args.command is None:
            # No command provided, launch interactive mode
            cli.run_interactive()
        elif args.command == 'create':
            cli.handle_create(args.type, args.name)
        elif args.command == 'generate':
            cli.handle_generate(args.template)
        elif args.command == 'config':
            cli.handle_config(args.action, args.key, args.value)
        else:
            parser.print_help()
            
    except KeyboardInterrupt:
        print("\n👋 Goodbye!")
        sys.exit(0)
    except Exception as e:
        print(f"❌ Error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
