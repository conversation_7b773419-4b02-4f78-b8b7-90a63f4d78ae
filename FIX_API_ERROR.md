# 🔧 Fix: "No auth credentials found" Error

## ❌ The Problem
You're getting this error when trying to use the Gemini AI CLI:
```
❌ Error: HTTP Error 401: {"error":{"message":"No auth credentials found","code":401}}
```

## ✅ The Solution
The API key in the application has expired. You need to set up your own OpenRouter API key.

## 🚀 Quick Fix (3 Steps)

### Step 1: Get Your Free API Key
1. Visit [https://openrouter.ai/](https://openrouter.ai/)
2. Sign up for a free account
3. Go to your dashboard
4. Generate an API key (starts with `sk-or-v1-`)

### Step 2: Set Up the API Key
**Option A - Automatic Setup (Easiest):**
```bash
python setup_api_key.py
```

**Option B - Manual Setup:**

**Windows:**
```cmd
set OPENROUTER_API_KEY=your-api-key-here
```

**macOS/Linux:**
```bash
export OPENROUTER_API_KEY='your-api-key-here'
```

**Config File (Any Platform):**
```bash
echo 'your-api-key-here' > ~/.openrouter_key
```

### Step 3: Test It Works
```bash
python gemini_ai_cli.py
```

## 🔧 Detailed Instructions

### Method 1: Using the Setup Script (Recommended)

1. **Run the setup script**:
   ```bash
   python setup_api_key.py
   ```

2. **Follow the prompts**:
   - Enter your OpenRouter API key
   - Choose setup method (environment variable recommended)
   - Test the connection

3. **Done!** The application should now work.

### Method 2: Manual Environment Variable

**Windows (Command Prompt):**
```cmd
# Temporary (current session only)
set OPENROUTER_API_KEY=sk-or-v1-your-key-here

# Permanent
setx OPENROUTER_API_KEY sk-or-v1-your-key-here
```

**Windows (PowerShell):**
```powershell
# Temporary
$env:OPENROUTER_API_KEY="sk-or-v1-your-key-here"

# Permanent
[Environment]::SetEnvironmentVariable("OPENROUTER_API_KEY", "sk-or-v1-your-key-here", "User")
```

**macOS/Linux:**
```bash
# Temporary
export OPENROUTER_API_KEY='sk-or-v1-your-key-here'

# Permanent (add to ~/.bashrc or ~/.zshrc)
echo 'export OPENROUTER_API_KEY="sk-or-v1-your-key-here"' >> ~/.bashrc
source ~/.bashrc
```

### Method 3: Config File

Create a file in your home directory:
```bash
# Create the config file
echo 'sk-or-v1-your-key-here' > ~/.openrouter_key

# Set proper permissions (Unix/Linux/macOS only)
chmod 600 ~/.openrouter_key
```

## 🧪 Testing Your Setup

### Test 1: Check if API key is set
**Windows:**
```cmd
echo %OPENROUTER_API_KEY%
```

**macOS/Linux:**
```bash
echo $OPENROUTER_API_KEY
```

### Test 2: Check config file
```bash
cat ~/.openrouter_key
```

### Test 3: Run the application
```bash
python gemini_ai_cli.py
```

If you see the welcome message without errors, it's working!

## 🆘 Still Having Issues?

### Common Problems and Solutions

**Problem**: "API key doesn't look like an OpenRouter key"
- **Solution**: Make sure your key starts with `sk-or-v1-`

**Problem**: Environment variable not persisting
- **Windows**: Use `setx` instead of `set`
- **macOS/Linux**: Add to your shell profile file

**Problem**: "Permission denied" when creating config file
- **Solution**: Check you have write permissions to your home directory

**Problem**: Still getting 401 error
- **Solution**: 
  1. Double-check your API key is correct
  2. Make sure there are no extra spaces
  3. Try generating a new API key

### Get Help

1. **Run the diagnostic script**:
   ```bash
   python setup_api_key.py
   ```

2. **Check the detailed guide**: [API_KEY_SETUP.md](API_KEY_SETUP.md)

3. **Verify your OpenRouter account** has an active API key

## 💡 Why This Happened

The original API key embedded in the application was:
- A demo/example key that expired
- Not meant for production use
- Shared publicly (which is a security risk)

Now the application properly handles user-provided API keys, which is:
- ✅ More secure
- ✅ Allows you to control usage
- ✅ Works with your own OpenRouter account

## 🎉 After Setup

Once your API key is configured, you can:
- Use the Gemini AI CLI normally
- Chat with the AI assistant
- Create projects and files
- Enjoy all the features!

The API key setup is a one-time process. After it's done, the application will work seamlessly.

---

**Need more help?** Check [API_KEY_SETUP.md](API_KEY_SETUP.md) for detailed instructions.
