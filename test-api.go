package main

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"
)

// Simple test to verify API connectivity
func main() {
	fmt.Println("🧪 Testing OpenRouter API connection...")
	fmt.Println("=====================================")

	const (
		openRouterURL = "https://openrouter.ai/api/v1/chat/completions"
		apiKey        = "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"
		model         = "deepseek/deepseek-chat-v3-0324:free"
	)

	type Message struct {
		Role    string `json:"role"`
		Content string `json:"content"`
	}

	type ChatRequest struct {
		Model    string    `json:"model"`
		Messages []Message `json:"messages"`
	}

	type Choice struct {
		Message Message `json:"message"`
	}

	type ChatResponse struct {
		Choices []Choice `json:"choices"`
		Error   *APIError `json:"error,omitempty"`
	}

	type APIError struct {
		Message string `json:"message"`
		Type    string `json:"type"`
		Code    string `json:"code"`
	}

	// Create test request
	request := ChatRequest{
		Model: model,
		Messages: []Message{
			{
				Role:    "user",
				Content: "Hello! Please respond with 'API test successful' if you can read this.",
			},
		},
	}

	// Marshal to JSON
	jsonData, err := json.Marshal(request)
	if err != nil {
		fmt.Printf("❌ Failed to marshal request: %v\n", err)
		return
	}

	// Create HTTP request
	req, err := http.NewRequest("POST", openRouterURL, bytes.NewBuffer(jsonData))
	if err != nil {
		fmt.Printf("❌ Failed to create request: %v\n", err)
		return
	}

	// Set headers
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer "+apiKey)

	// Create client with timeout
	client := &http.Client{
		Timeout: 30 * time.Second,
	}

	fmt.Println("📡 Sending test request...")

	// Send request
	resp, err := client.Do(req)
	if err != nil {
		fmt.Printf("❌ Failed to send request: %v\n", err)
		fmt.Println("\n🔍 Possible issues:")
		fmt.Println("   - Check your internet connection")
		fmt.Println("   - Verify firewall settings")
		fmt.Println("   - Ensure the API endpoint is accessible")
		return
	}
	defer resp.Body.Close()

	// Read response
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		fmt.Printf("❌ Failed to read response: %v\n", err)
		return
	}

	fmt.Printf("📊 HTTP Status: %d\n", resp.StatusCode)

	// Check status code
	if resp.StatusCode != http.StatusOK {
		fmt.Printf("❌ API request failed with status %d\n", resp.StatusCode)
		fmt.Printf("Response body: %s\n", string(body))
		return
	}

	// Parse response
	var chatResponse ChatResponse
	if err := json.Unmarshal(body, &chatResponse); err != nil {
		fmt.Printf("❌ Failed to parse response: %v\n", err)
		fmt.Printf("Raw response: %s\n", string(body))
		return
	}

	// Check for API errors
	if chatResponse.Error != nil {
		fmt.Printf("❌ API error: %s\n", chatResponse.Error.Message)
		return
	}

	// Check response
	if len(chatResponse.Choices) == 0 {
		fmt.Println("❌ No response choices received")
		return
	}

	response := chatResponse.Choices[0].Message.Content
	fmt.Printf("✅ API Response: %s\n", response)
	fmt.Println("\n🎉 API test completed successfully!")
	fmt.Println("   The main application should work correctly.")
}
