@echo off
setlocal enabledelayedexpansion

REM ============================================================================
REM Craft Code CLI - Windows Installer
REM Automatically installs Python (if needed) and sets up global codecraft command
REM ============================================================================

title Craft Code CLI - Installer

echo.
echo ╔══════════════════════════════════════════════════════════════════════════╗
echo ║                        🚀 Craft Code CLI Installer                      ║
echo ║                              by CodeCraft                               ║
echo ╚══════════════════════════════════════════════════════════════════════════╝
echo.

REM Get the directory where this script is located
set "INSTALL_DIR=%~dp0"
set "INSTALL_DIR=%INSTALL_DIR:~0,-1%"

echo 📁 Installation directory: %INSTALL_DIR%
echo.

REM ============================================================================
REM Step 1: Check for Python installation
REM ============================================================================

echo [1/6] 🔍 Checking for Python installation...

python --version >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    for /f "tokens=2" %%i in ('python --version 2^>^&1') do set PYTHON_VERSION=%%i
    echo ✅ Python !PYTHON_VERSION! is already installed
    set PYTHON_INSTALLED=1
) else (
    echo ❌ Python is not installed or not in PATH
    set PYTHON_INSTALLED=0
)

REM ============================================================================
REM Step 2: Install Python if needed
REM ============================================================================

if !PYTHON_INSTALLED! EQU 0 (
    echo.
    echo [2/6] 📥 Installing Python...
    echo.
    echo This will download and install Python 3.11 from python.org
    echo The installer will open - please follow these steps:
    echo   1. ✅ Check "Add Python to PATH"
    echo   2. ✅ Click "Install Now"
    echo   3. ✅ Wait for installation to complete
    echo.
    
    set /p CONFIRM="Continue with Python installation? (Y/n): "
    if /i "!CONFIRM!" NEQ "Y" if /i "!CONFIRM!" NEQ "" (
        echo ❌ Installation cancelled by user
        pause
        exit /b 1
    )
    
    echo 📡 Downloading Python installer...
    
    REM Create temp directory
    set "TEMP_DIR=%TEMP%\codecraft_install"
    if not exist "!TEMP_DIR!" mkdir "!TEMP_DIR!"
    
    REM Download Python installer
    set "PYTHON_INSTALLER=!TEMP_DIR!\python-installer.exe"
    powershell -Command "& {[Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls12; Invoke-WebRequest -Uri 'https://www.python.org/ftp/python/3.11.9/python-3.11.9-amd64.exe' -OutFile '!PYTHON_INSTALLER!'}"
    
    if not exist "!PYTHON_INSTALLER!" (
        echo ❌ Failed to download Python installer
        echo Please download Python manually from https://www.python.org/downloads/
        pause
        exit /b 1
    )
    
    echo ✅ Python installer downloaded
    echo 🚀 Launching Python installer...
    echo.
    echo ⚠️  IMPORTANT: Make sure to check "Add Python to PATH" during installation!
    echo.
    pause
    
    REM Run Python installer with automatic PATH addition
    "!PYTHON_INSTALLER!" /quiet InstallAllUsers=1 PrependPath=1 Include_test=0
    
    REM Wait a moment for installation to complete
    timeout /t 5 /nobreak >nul
    
    REM Clean up
    del "!PYTHON_INSTALLER!" >nul 2>&1
    rmdir "!TEMP_DIR!" >nul 2>&1
    
    REM Refresh environment variables
    echo 🔄 Refreshing environment variables...
    call :RefreshEnv
    
    REM Check if Python is now available
    python --version >nul 2>&1
    if %ERRORLEVEL% NEQ 0 (
        echo ❌ Python installation may have failed or PATH not updated
        echo Please restart your command prompt and run this installer again
        echo Or install Python manually from https://www.python.org/downloads/
        pause
        exit /b 1
    )
    
    for /f "tokens=2" %%i in ('python --version 2^>^&1') do set PYTHON_VERSION=%%i
    echo ✅ Python !PYTHON_VERSION! installed successfully
) else (
    echo [2/6] ⏭️  Python already installed, skipping...
)

REM ============================================================================
REM Step 3: Upgrade pip and install dependencies
REM ============================================================================

echo.
echo [3/6] 📦 Installing dependencies...

REM Upgrade pip first
echo 🔄 Upgrading pip...
python -m pip install --upgrade pip >nul 2>&1

REM Install required packages
echo 📚 Installing required packages...
python -m pip install rich >nul 2>&1

if %ERRORLEVEL% NEQ 0 (
    echo ❌ Failed to install dependencies
    echo Please check your internet connection and try again
    pause
    exit /b 1
)

echo ✅ Dependencies installed successfully

REM ============================================================================
REM Step 4: Create global command script
REM ============================================================================

echo.
echo [4/6] 🌐 Creating global 'codecraft' command...

REM Find Python Scripts directory
for /f "tokens=*" %%i in ('python -c "import sys; print(sys.prefix)"') do set PYTHON_PREFIX=%%i
set "SCRIPTS_DIR=%PYTHON_PREFIX%\Scripts"

if not exist "%SCRIPTS_DIR%" (
    echo ❌ Python Scripts directory not found: %SCRIPTS_DIR%
    pause
    exit /b 1
)

REM Create codecraft.bat in Scripts directory
set "CODECRAFT_BAT=%SCRIPTS_DIR%\codecraft.bat"

echo @echo off > "%CODECRAFT_BAT%"
echo python "%INSTALL_DIR%\src\main.py" %%* >> "%CODECRAFT_BAT%"

if not exist "%CODECRAFT_BAT%" (
    echo ❌ Failed to create global command script
    echo You may need to run this installer as Administrator
    pause
    exit /b 1
)

echo ✅ Global 'codecraft' command created

REM ============================================================================
REM Step 5: Verify PATH configuration
REM ============================================================================

echo.
echo [5/6] 🔍 Verifying PATH configuration...

REM Check if Scripts directory is in PATH
echo %PATH% | findstr /i "%SCRIPTS_DIR%" >nul
if %ERRORLEVEL% NEQ 0 (
    echo ⚠️  Python Scripts directory not in PATH
    echo Adding to PATH for current session...
    set "PATH=%PATH%;%SCRIPTS_DIR%"
    
    echo 📝 To make this permanent, please add the following to your system PATH:
    echo    %SCRIPTS_DIR%
    echo.
) else (
    echo ✅ Python Scripts directory is in PATH
)

REM ============================================================================
REM Step 6: Test installation
REM ============================================================================

echo.
echo [6/6] 🧪 Testing installation...

REM Test the codecraft command
codecraft --version >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo ✅ Installation test passed
) else (
    echo ⚠️  Installation test failed - command may not be globally available yet
    echo You may need to restart your command prompt
)

REM ============================================================================
REM Installation Complete
REM ============================================================================

echo.
echo ╔══════════════════════════════════════════════════════════════════════════╗
echo ║                     🎉 Installation Complete!                          ║
echo ╚══════════════════════════════════════════════════════════════════════════╝
echo.
echo ✅ Craft Code CLI has been installed successfully!
echo.
echo 🚀 Usage:
echo    codecraft              # Launch interactive mode
echo    codecraft --help       # Show help
echo    codecraft --version    # Show version
echo.
echo 📁 Installation location: %INSTALL_DIR%
echo 🌐 Global command: codecraft
echo.
echo 💡 If the 'codecraft' command doesn't work immediately:
echo    1. Restart your command prompt/terminal
echo    2. Or run: %CODECRAFT_BAT%
echo.
echo 📖 For more information, visit:
echo    https://github.com/CodeCraft/craft-code-cli
echo.

pause
goto :eof

REM ============================================================================
REM Helper Functions
REM ============================================================================

:RefreshEnv
REM Refresh environment variables without restarting
for /f "tokens=2*" %%a in ('reg query "HKLM\SYSTEM\CurrentControlSet\Control\Session Manager\Environment" /v PATH 2^>nul') do set "SysPath=%%b"
for /f "tokens=2*" %%a in ('reg query "HKCU\Environment" /v PATH 2^>nul') do set "UserPath=%%b"
set "PATH=%SysPath%;%UserPath%"
goto :eof
