# 📖 Craft Code CLI - User Guide

Complete guide to using Craft Code CLI for your development workflow.

## 🚀 Getting Started

### First Launch

After installation, launch Craft Code CLI:

```bash
codecraft
```

On first run, you'll be guided through a setup wizard:

1. **Enter your name** (for personalization)
2. **Choose preferred programming language**
3. **Set default project directory**

### Interactive Mode

The main interface provides an interactive command prompt:

```
🎯 codecraft> 
```

Type `help` to see available commands.

## 📋 Commands Reference

### Core Commands

#### `help`
Display available commands and usage information.

```bash
codecraft> help
```

#### `create project <name>`
Create a new project with standard structure.

```bash
codecraft> create project MyAwesomeApp
```

Creates:
```
MyAwesomeApp/
├── src/
├── tests/
├── docs/
├── README.md
└── .gitignore
```

#### `create file <name>`
Create a new file with appropriate template.

```bash
codecraft> create file main.py
codecraft> create file app.js
codecraft> create file Main.java
```

#### `generate`
Access code generation templates.

```bash
codecraft> generate
```

Available templates:
- Flask web application
- FastAPI application
- React component
- Python class template

#### `config`
Manage configuration settings.

```bash
# List all settings
codecraft> config list

# Get specific setting
codecraft> config get user_name

# Set a value
codecraft> config set default_language javascript
```

### Command Line Usage

You can also use Craft Code CLI directly from command line:

```bash
# Show version
codecraft --version

# Show help
codecraft --help

# Create project directly
codecraft create project MyApp

# Generate templates
codecraft generate flask-app
```

## 🛠 Project Management

### Creating Projects

Craft Code CLI creates well-structured projects:

```bash
codecraft create project WebApp
```

**Project Structure:**
- `src/` - Source code
- `tests/` - Test files
- `docs/` - Documentation
- `README.md` - Project description
- `.gitignore` - Git ignore rules

### Project Templates

Different project types coming soon:
- Web applications
- Desktop applications
- CLI tools
- Libraries/packages

## 📝 Code Generation

### File Templates

When creating files, Craft Code CLI provides appropriate templates:

**Python (.py):**
```python
#!/usr/bin/env python3
"""
Module description
"""

def main():
    """Main function"""
    pass

if __name__ == "__main__":
    main()
```

**JavaScript (.js):**
```javascript
/**
 * Module description
 */

function main() {
    // TODO: Implement
}

if (require.main === module) {
    main();
}
```

### Advanced Templates

Use the `generate` command for complex templates:

```bash
codecraft> generate
```

Select from available templates and follow prompts.

## ⚙️ Configuration

### Configuration File

Settings are stored in `~/.codecraft/config.json`:

```json
{
  "user_name": "Developer",
  "default_language": "python",
  "project_directory": "/home/<USER>/CodeCraft-Projects",
  "theme": "default"
}
```

### Available Settings

| Setting | Description | Default |
|---------|-------------|---------|
| `user_name` | Your name for personalization | "Developer" |
| `default_language` | Preferred programming language | "python" |
| `project_directory` | Where projects are created | "~/CodeCraft-Projects" |
| `theme` | CLI theme (future feature) | "default" |

### Changing Settings

```bash
# Interactive mode
codecraft> config set user_name "John Doe"
codecraft> config set default_language javascript
codecraft> config set project_directory "/path/to/projects"

# Command line
codecraft config set user_name "John Doe"
```

## 🎨 Customization

### Themes (Coming Soon)

Future versions will support themes:
- Dark theme
- Light theme
- Custom color schemes

### Custom Templates

You'll be able to add custom code templates:
- Personal boilerplate code
- Company-specific templates
- Framework-specific structures

## 🔧 Advanced Usage

### Integration with IDEs

Craft Code CLI works well with:
- **VS Code**: Open projects with `code .`
- **PyCharm**: Open projects with `pycharm .`
- **Sublime Text**: Open with `subl .`

### Git Integration (Coming Soon)

Future features:
- Initialize Git repositories
- Create initial commits
- Set up remote repositories

### Package Management (Coming Soon)

Planned features:
- Install dependencies
- Manage virtual environments
- Update packages

## 📊 Productivity Features

### Project Analytics (Coming Soon)

Track your coding productivity:
- Projects created
- Files generated
- Time spent coding
- Language usage statistics

### Workflow Automation (Coming Soon)

Automate common tasks:
- Project setup workflows
- Code formatting
- Testing automation
- Deployment scripts

## 🐛 Troubleshooting

### Common Issues

#### Command Not Working
```bash
# Check if command exists
which codecraft

# Run directly if needed
python /path/to/craft-code/src/main.py
```

#### Configuration Issues
```bash
# Reset configuration
rm -rf ~/.codecraft

# Reconfigure on next run
codecraft
```

#### Permission Errors
```bash
# Check file permissions
ls -la ~/.codecraft/

# Fix permissions if needed
chmod 755 ~/.codecraft/
chmod 644 ~/.codecraft/config.json
```

### Getting Help

1. Use built-in help: `codecraft --help`
2. Check documentation: [GitHub Wiki](https://github.com/CodeCraft/craft-code-cli/wiki)
3. Report issues: [GitHub Issues](https://github.com/CodeCraft/craft-code-cli/issues)

## 🚀 Tips & Tricks

### Keyboard Shortcuts

In interactive mode:
- `Ctrl+C` - Exit current operation
- `Ctrl+D` - Exit application
- `Tab` - Auto-completion (future feature)

### Quick Commands

```bash
# Quick project creation
codecraft create project $(date +%Y%m%d)-experiment

# Batch file creation
for file in main.py utils.py config.py; do
    codecraft create file $file
done
```

### Workflow Examples

**Starting a new Python project:**
```bash
codecraft create project my-python-app
cd ~/CodeCraft-Projects/my-python-app
codecraft create file main.py
codecraft create file requirements.txt
```

**Web development workflow:**
```bash
codecraft create project my-web-app
cd ~/CodeCraft-Projects/my-web-app
codecraft generate flask-app
```

## 📈 What's Next?

### Upcoming Features

- 🎨 Custom themes and colors
- 🔧 Plugin system
- 📊 Productivity analytics
- 🌐 Cloud synchronization
- 🤖 AI-powered code suggestions
- 📱 Mobile companion app

### Contributing

Want to contribute? Check out:
- [Contributing Guide](CONTRIBUTING.md)
- [Development Setup](docs/DEVELOPMENT.md)
- [Feature Requests](https://github.com/CodeCraft/craft-code-cli/discussions)

---

**Happy coding! 🎉**
