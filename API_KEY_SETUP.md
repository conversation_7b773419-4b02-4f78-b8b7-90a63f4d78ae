# 🔑 API Key Setup Guide

The Gemini AI CLI requires an OpenRouter API key to function. The original API key has expired, so you need to set up your own.

## 🚀 Quick Setup

### Option 1: Automatic Setup (Recommended)
```bash
python setup_api_key.py
```

### Option 2: Manual Setup

1. **Get your API key**:
   - Visit [https://openrouter.ai/](https://openrouter.ai/)
   - Sign up for a free account
   - Go to your dashboard and generate an API key

2. **Set the API key** (choose one method):

   **Method A - Environment Variable (Windows):**
   ```cmd
   set OPENROUTER_API_KEY=your-api-key-here
   ```
   
   **Method A - Environment Variable (macOS/Linux):**
   ```bash
   export OPENROUTER_API_KEY='your-api-key-here'
   ```
   
   **Method B - Config File:**
   ```bash
   echo 'your-api-key-here' > ~/.openrouter_key
   ```

3. **Test the setup**:
   ```bash
   python gemini_ai_cli.py
   ```

## 🔧 Troubleshooting

### "No auth credentials found" Error
This means your API key isn't set up correctly. Try:

1. **Check if API key is set**:
   ```bash
   # Windows
   echo %OPENROUTER_API_KEY%
   
   # macOS/Linux
   echo $OPENROUTER_API_KEY
   ```

2. **Check config file**:
   ```bash
   cat ~/.openrouter_key
   ```

3. **Run the setup script**:
   ```bash
   python setup_api_key.py
   ```

### "Invalid API key" Error
- Make sure your API key starts with `sk-or-v1-`
- Check that you copied the full key without extra spaces
- Verify the key is active in your OpenRouter dashboard

### Environment Variable Not Persisting
**Windows:**
```cmd
setx OPENROUTER_API_KEY your-api-key-here
```

**macOS/Linux:**
Add to your shell profile (~/.bashrc, ~/.zshrc, or ~/.profile):
```bash
export OPENROUTER_API_KEY='your-api-key-here'
```

## 💡 Tips

- **Free Tier**: OpenRouter offers free usage for many models
- **Security**: Keep your API key private and don't share it
- **Multiple Projects**: You can use the same API key for multiple projects
- **Config File**: The `~/.openrouter_key` method works across all your Python projects

## 🆘 Still Having Issues?

1. **Run the diagnostic**:
   ```bash
   python setup_api_key.py
   ```

2. **Check the error message** - it usually tells you exactly what's wrong

3. **Try a fresh API key** from OpenRouter dashboard

4. **Contact support** if the issue persists

---

Once your API key is set up, you can use the Gemini AI CLI normally:
```bash
python gemini_ai_cli.py
```
