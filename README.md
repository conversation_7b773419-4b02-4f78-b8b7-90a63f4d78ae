# Gemini-like AI CLI Assistant

A standalone command-line AI assistant powered by DeepSeek via OpenRouter, similar to Google's Gemini AI. This application runs on Windows without requiring any pre-installed programming languages or runtimes.

## Features

- 🤖 **AI-Powered Conversations**: Chat with DeepSeek AI model through OpenRouter
- 💻 **Standalone Executable**: No dependencies required - just run the .exe file
- 🎯 **Rich CLI Interface**: Beautiful command-line interface with colors and animations
- 🔒 **Secure**: API key is embedded securely in the compiled executable
- ⚡ **Fast**: Lightweight Python application with minimal resource usage
- 🎨 **User-Friendly**: Spinning indicators, help commands, and intuitive controls

## Quick Start

### Option 1: Use Pre-built Executable (Recommended)
If you have the pre-built `gemini-ai.exe` file:
1. Double-click `gemini-ai.exe` to run
2. Or open Command Prompt/PowerShell and run:
   ```cmd
   .\gemini-ai.exe
   ```

### Option 2: Build from Source (Automatic Installation)
**Easiest method - PowerShell script handles everything:**
1. Right-click on `install-and-build.ps1` and select "Run with PowerShell"
2. Or open PowerShell as Administrator and run:
   ```powershell
   powershell -ExecutionPolicy Bypass -File install-and-build.ps1
   ```
3. The script will:
   - Check if Go is installed
   - Download and install Go if needed
   - Build the executable automatically
   - Create `gemini-ai.exe` ready to use

### Option 3: Build from Source (Manual)
1. **Install Go** (if not already installed):
   - Visit: https://golang.org/dl/
   - Download Go for Windows (64-bit)
   - Run the installer and follow the setup wizard
   - Restart your terminal/command prompt

2. **Build the application**:
   ```cmd
   build.bat
   ```

3. **Run the executable**:
   ```cmd
   .\gemini-ai.exe
   ```

### Option 4: Quick Build (if Go is already installed)
```cmd
go build -ldflags="-s -w" -o gemini-ai.exe main.go
.\gemini-ai.exe
```

## Usage

Once the application starts, you'll see a welcome message. Simply type your questions or prompts and press Enter. The AI will respond using the DeepSeek model.

### Available Commands
- Type any question or prompt to chat with the AI
- `help` - Show help information
- `clear` - Clear the screen
- `exit` or `quit` - Exit the application

### Example Session
```
💬 You: What is the capital of France?
🤖 AI: The capital of France is Paris.

💬 You: Write a short poem about coding
🤖 AI: Lines of code dance on the screen,
Logic flows like a crystal stream,
Bugs hide in shadows, waiting to be found,
While algorithms make their rhythmic sound.

💬 You: exit
👋 Goodbye!
```

## Technical Details

- **Language**: Python 3.13+
- **AI Model**: DeepSeek Chat v3 (via OpenRouter)
- **API**: OpenRouter AI API
- **Dependencies**: requests, rich (bundled in executable)
- **Build Tool**: PyInstaller
- **Platform**: Windows (amd64)
- **Executable Size**: ~15MB standalone

## API Configuration

The application is pre-configured with:
- **API Endpoint**: OpenRouter AI API
- **Model**: `deepseek/deepseek-chat-v3-0324:free`
- **API Key**: Embedded in the executable

## Error Handling

The application includes comprehensive error handling for:
- Network connectivity issues
- API rate limits and errors
- Invalid responses
- User input validation

## Security Notes

- The API key is embedded in the compiled executable
- All communication with the API is over HTTPS
- No user data is stored locally

## Troubleshooting

### Common Issues

1. **"Network error"**: Check your internet connection
2. **"API error"**: The service might be temporarily unavailable
3. **Application won't start**: Ensure you're running on a 64-bit Windows system

### Getting Help

If you encounter issues:
1. Check your internet connection
2. Try running the application as administrator
3. Ensure Windows Defender or antivirus isn't blocking the executable

## License

This project is provided as-is for educational and personal use.
#   c r a f t c o d e 
 
 