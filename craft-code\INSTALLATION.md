# 📦 Craft Code CLI - Installation Guide

Complete installation instructions for all platforms.

## 🚀 Quick Start

### Windows (Recommended Method)

1. **Download** the repository:
   ```cmd
   git clone https://github.com/CodeCraft/craft-code-cli.git
   cd craft-code-cli
   ```
   
   Or download ZIP and extract.

2. **Run the installer**:
   ```cmd
   install.bat
   ```

3. **Use the global command**:
   ```cmd
   codecraft
   ```

### macOS / Linux

1. **Download** the repository:
   ```bash
   git clone https://github.com/CodeCraft/craft-code-cli.git
   cd craft-code-cli
   ```

2. **Run the installer**:
   ```bash
   chmod +x install.sh
   ./install.sh
   ```

3. **Use the global command**:
   ```bash
   codecraft
   ```

### Cross-Platform Python Method

1. **Download** the repository
2. **Run the Python installer**:
   ```bash
   python setup.py install
   ```

## 📋 System Requirements

### Minimum Requirements
- **Operating System**: Windows 10/11, macOS 10.14+, or Linux
- **Python**: 3.8 or higher (automatically installed if missing)
- **Memory**: 100MB RAM
- **Storage**: 50MB free space
- **Internet**: Required for initial setup

### Supported Platforms
- ✅ **Windows 10/11** (Primary support)
- ✅ **macOS** (Intel and Apple Silicon)
- ✅ **Linux** (Ubuntu, Debian, CentOS, Fedora, Arch)

## 🔧 Installation Methods

### Method 1: Automatic Installer (Recommended)

**Windows:**
```cmd
# Download and run
install.bat
```

**macOS/Linux:**
```bash
# Make executable and run
chmod +x install.sh
./install.sh
```

**What it does:**
- ✅ Detects Python installation
- ✅ Downloads and installs Python if missing
- ✅ Installs required dependencies
- ✅ Creates global `codecraft` command
- ✅ Configures PATH automatically
- ✅ Tests installation

### Method 2: Python Setup Script

```bash
# Cross-platform Python installer
python setup.py install
```

**Features:**
- ✅ Works on all platforms
- ✅ Handles Python version checking
- ✅ Installs dependencies
- ✅ Creates global command
- ✅ Configures shell profiles

### Method 3: Manual Installation

1. **Install Python 3.8+** manually from [python.org](https://www.python.org/downloads/)

2. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

3. **Create global command** (Windows):
   ```cmd
   # Add to Python Scripts directory
   echo @echo off > %PYTHON_PREFIX%\Scripts\codecraft.bat
   echo python "%CD%\src\main.py" %%* >> %PYTHON_PREFIX%\Scripts\codecraft.bat
   ```

4. **Create global command** (Unix):
   ```bash
   # Create executable script
   echo '#!/bin/bash' > ~/.local/bin/codecraft
   echo 'python3 "'$(pwd)'/src/main.py" "$@"' >> ~/.local/bin/codecraft
   chmod +x ~/.local/bin/codecraft
   ```

## 🛠 Troubleshooting

### Common Issues

#### "Python not found" Error
**Problem**: Python is not installed or not in PATH
**Solution**:
- Windows: Download from [python.org](https://www.python.org/downloads/) and check "Add to PATH"
- macOS: Install via Homebrew: `brew install python@3.11`
- Linux: Use package manager: `sudo apt install python3 python3-pip`

#### "codecraft command not found"
**Problem**: Global command not created or PATH not updated
**Solutions**:
1. Restart terminal/command prompt
2. Check PATH includes Python Scripts directory
3. Run directly: `python path/to/craft-code/src/main.py`

#### Permission Denied (Linux/macOS)
**Problem**: Insufficient permissions to create global command
**Solutions**:
1. Run installer with sudo: `sudo ./install.sh`
2. Install to user directory: Uses `~/.local/bin` automatically
3. Manual installation to user directory

#### Dependencies Installation Failed
**Problem**: Network issues or pip problems
**Solutions**:
1. Check internet connection
2. Upgrade pip: `python -m pip install --upgrade pip`
3. Use user installation: `pip install --user rich`

### Advanced Troubleshooting

#### Check Python Installation
```bash
# Check Python version
python --version
python3 --version

# Check pip
pip --version
pip3 --version

# Check installation location
python -c "import sys; print(sys.prefix)"
```

#### Check PATH Configuration
```bash
# Windows
echo %PATH%

# Unix
echo $PATH
```

#### Manual Dependency Installation
```bash
# Install specific version
pip install rich==13.0.0

# Install with user flag
pip install --user rich

# Install from requirements file
pip install -r requirements.txt
```

#### Verify Installation
```bash
# Test Python import
python -c "import sys; sys.path.insert(0, 'src'); import main"

# Test command execution
python src/main.py --version

# Test global command
codecraft --version
```

## 🔄 Updating

### Update from Git
```bash
# Pull latest changes
git pull origin main

# Reinstall dependencies
pip install -r requirements.txt
```

### Reinstall
```bash
# Windows
install.bat

# Unix
./install.sh
```

## 🗑 Uninstallation

### Remove Global Command
```bash
# Windows
del "%PYTHON_PREFIX%\Scripts\codecraft.bat"

# Unix
rm ~/.local/bin/codecraft
# or
sudo rm /usr/local/bin/codecraft
```

### Remove Dependencies
```bash
pip uninstall rich
```

### Remove Configuration
```bash
# Remove user configuration
rm -rf ~/.codecraft
```

## 📞 Getting Help

If you encounter issues:

1. **Check this guide** for common solutions
2. **Verify system requirements**
3. **Try manual installation method**
4. **Check GitHub Issues**: [Issues Page](https://github.com/CodeCraft/craft-code-cli/issues)
5. **Create new issue** with:
   - Operating system and version
   - Python version
   - Error messages
   - Installation method used

## 🎯 Next Steps

After successful installation:

1. **Run the application**: `codecraft`
2. **Complete first-time setup**
3. **Read the user guide**: [docs/USER_GUIDE.md](docs/USER_GUIDE.md)
4. **Explore features**: `codecraft --help`

---

**Happy coding with Craft Code CLI! 🚀**
