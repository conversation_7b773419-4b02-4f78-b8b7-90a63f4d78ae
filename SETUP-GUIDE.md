# 🚀 Gemini AI CLI - Complete Setup Guide

This guide will help you get the Gemini AI CLI running on your Windows system, even if you don't have any programming tools installed.

## 📋 What You'll Get

A standalone Windows executable (`gemini-ai.exe`) that:
- ✅ Works without installing Python, Node.js, or any other runtime
- ✅ Connects to DeepSeek AI via OpenRouter API
- ✅ Provides a simple command-line chat interface
- ✅ Handles errors gracefully
- ✅ Is completely portable (copy to any Windows PC and run)

## 🎯 Quick Start (3 Methods)

### Method 1: Automatic Setup (Recommended)
**Best for beginners - everything is automated:**

1. **Download all files** to a folder on your computer
2. **Right-click** on `install-and-build.ps1`
3. **Select** "Run with PowerShell"
4. **Follow the prompts** - the script will:
   - Check if Go is installed
   - Download and install Go if needed
   - Build the executable automatically
5. **Run** `gemini-ai.exe` when complete

### Method 2: Using Command Prompt/PowerShell
1. **Open PowerShell as Administrator**
2. **Navigate** to the folder containing the files:
   ```powershell
   cd "C:\path\to\your\folder"
   ```
3. **Run the setup script**:
   ```powershell
   powershell -ExecutionPolicy Bypass -File install-and-build.ps1
   ```
4. **Run** `gemini-ai.exe` when complete

### Method 3: Manual Setup
If you prefer to do everything manually:

1. **Install Go**:
   - Visit: https://golang.org/dl/
   - Download "go1.21.x.windows-amd64.msi"
   - Run the installer
   - Restart your terminal

2. **Build the application**:
   - Open Command Prompt in the project folder
   - Run: `build.bat`
   - Wait for compilation to complete

3. **Run the application**:
   - Double-click `gemini-ai.exe` or
   - Run: `.\gemini-ai.exe`

## 🔧 Troubleshooting

### "PowerShell execution policy" error
If you get an execution policy error:
```powershell
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
```
Then try running the script again.

### "Go is not recognized" error
This means Go isn't installed or isn't in your PATH:
1. Install Go from https://golang.org/dl/
2. Restart your terminal
3. Try again

### "Network error" when running the app
- Check your internet connection
- Try running as administrator
- Check if your firewall is blocking the application

### Antivirus blocking the executable
Some antivirus software may flag the executable:
1. Add the folder to your antivirus exclusions
2. Or temporarily disable real-time protection during build
3. The executable is safe - it only makes HTTPS requests to OpenRouter

## 🧪 Testing the Setup

Before building the main application, you can test the API connection:

1. **Build the test tool**:
   ```cmd
   go build -o test-api.exe test-api.go
   ```

2. **Run the test**:
   ```cmd
   .\test-api.exe
   ```

This will verify that the API connection works correctly.

## 📁 File Structure

After setup, you'll have:
```
📁 Your Folder/
├── 📄 main.go                 # Main application source code
├── 📄 go.mod                  # Go module file
├── 📄 build.bat               # Windows build script
├── 📄 install-and-build.ps1   # Automatic setup script
├── 📄 test-api.go             # API connection test
├── 📄 README.md               # Documentation
├── 📄 SETUP-GUIDE.md          # This file
└── 🎯 gemini-ai.exe           # Your final executable!
```

## 🎮 Using the Application

Once `gemini-ai.exe` is built:

1. **Double-click** the executable, or
2. **Open Command Prompt** and run: `.\gemini-ai.exe`

### Available Commands:
- Type any question or prompt
- `help` - Show help information
- `clear` - Clear the screen
- `exit` or `quit` - Exit the application

### Example Usage:
```
💬 You: What is artificial intelligence?
🤖 AI: Artificial intelligence (AI) refers to...

💬 You: Write a Python function to calculate fibonacci numbers
🤖 AI: Here's a Python function to calculate Fibonacci numbers:
...

💬 You: exit
👋 Goodbye!
```

## 🔒 Security & Privacy

- ✅ All communication is encrypted (HTTPS)
- ✅ No data is stored locally
- ✅ API key is securely embedded in the executable
- ✅ No telemetry or tracking

## 📞 Support

If you encounter issues:

1. **Check this guide** for common solutions
2. **Verify your internet connection**
3. **Try running as administrator**
4. **Check Windows Defender/antivirus settings**

## 🎉 Success!

Once you see the welcome message, you're ready to chat with AI! The application will work on any Windows computer without requiring any additional software.
