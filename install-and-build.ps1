# PowerShell script to install Go and build the Gemini AI CLI
param(
    [switch]$SkipGoInstall
)

Write-Host "Gemini AI CLI - Installation and Build Script" -ForegroundColor Cyan
Write-Host "=================================================" -ForegroundColor Cyan
Write-Host ""

# Function to check if Go is installed
function Test-GoInstalled {
    try {
        $goVersion = go version 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-Host "Go is already installed: $goVersion" -ForegroundColor Green
            return $true
        }
    }
    catch {
        return $false
    }
    return $false
}

# Function to install Go
function Install-Go {
    Write-Host "Installing Go..." -ForegroundColor Yellow

    # Download Go installer
    $goVersion = "1.21.5"
    $goInstaller = "go$goVersion.windows-amd64.msi"
    $downloadUrl = "https://golang.org/dl/$goInstaller"
    $tempPath = "$env:TEMP\$goInstaller"

    try {
        Write-Host "   Downloading Go installer..." -ForegroundColor Gray
        Invoke-WebRequest -Uri $downloadUrl -OutFile $tempPath -UseBasicParsing

        Write-Host "   Running Go installer..." -ForegroundColor Gray
        Write-Host "   Please follow the installation wizard that will open." -ForegroundColor Yellow
        Start-Process -FilePath $tempPath -Wait

        # Refresh environment variables
        $machinePath = [System.Environment]::GetEnvironmentVariable("PATH", "Machine")
        $userPath = [System.Environment]::GetEnvironmentVariable("PATH", "User")
        $env:PATH = $machinePath + ";" + $userPath

        # Clean up
        Remove-Item $tempPath -ErrorAction SilentlyContinue

        Write-Host "Go installation completed!" -ForegroundColor Green
        Write-Host "   Please restart this script or open a new terminal to continue." -ForegroundColor Yellow
        return $true
    }
    catch {
        Write-Host "Failed to install Go: $_" -ForegroundColor Red
        return $false
    }
}

# Function to build the application
function Build-Application {
    Write-Host "Building Gemini AI CLI..." -ForegroundColor Yellow

    try {
        # Set environment variables for Windows build
        $env:GOOS = "windows"
        $env:GOARCH = "amd64"
        $env:CGO_ENABLED = "0"

        # Build the executable
        Write-Host "   Compiling application..." -ForegroundColor Gray
        go build -ldflags="-s -w" -o gemini-ai.exe main.go

        if ($LASTEXITCODE -eq 0) {
            Write-Host "Build successful!" -ForegroundColor Green
            Write-Host "Executable created: gemini-ai.exe" -ForegroundColor Green
            Write-Host ""
            Write-Host "Ready to use! Run the application with:" -ForegroundColor Cyan
            Write-Host "   .\gemini-ai.exe" -ForegroundColor White
            return $true
        }
        else {
            Write-Host "Build failed!" -ForegroundColor Red
            return $false
        }
    }
    catch {
        Write-Host "Build error: $_" -ForegroundColor Red
        return $false
    }
}

# Main execution
if (-not $SkipGoInstall) {
    if (-not (Test-GoInstalled)) {
        Write-Host "Go is not installed on this system." -ForegroundColor Yellow
        $install = Read-Host "Would you like to install Go automatically? (y/n)"

        if ($install -eq "y" -or $install -eq "Y") {
            if (Install-Go) {
                Write-Host ""
                Write-Host "Please restart this script after Go installation is complete." -ForegroundColor Yellow
                exit 0
            }
            else {
                Write-Host "Please install Go manually from https://golang.org/dl/ and run this script again." -ForegroundColor Yellow
                exit 1
            }
        }
        else {
            Write-Host "Please install Go manually from https://golang.org/dl/ and run this script again." -ForegroundColor Yellow
            exit 1
        }
    }
}

# Build the application
if (Test-GoInstalled) {
    Build-Application
}
else {
    Write-Host "Go is not available. Please install Go first." -ForegroundColor Red
    exit 1
}
