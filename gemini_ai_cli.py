#!/usr/bin/env python3
"""
Gemini-like AI CLI Assistant
A standalone command-line AI assistant powered by DeepSeek via OpenRouter
"""

import json
import os
import sys
import time
from typing import Dict, List, Optional

try:
    import requests
except ImportError:
    print("❌ Error: 'requests' library not found.")
    print("Please install it with: pip install requests")
    sys.exit(1)

try:
    from rich.console import Console
    from rich.panel import Panel
    from rich.text import Text
    from rich.prompt import Prompt
    from rich.live import Live
    from rich.spinner import Spinner
    RICH_AVAILABLE = True
except ImportError:
    RICH_AVAILABLE = False
    print("⚠️  Warning: 'rich' library not found. Using basic interface.")
    print("For better experience, install with: pip install rich")

# Configuration
OPENROUTER_URL = "https://openrouter.ai/api/v1/chat/completions"
API_KEY = "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"
MODEL = "deepseek/deepseek-chat-v3-0324:free"
TIMEOUT = 30


class AIClient:
    """Handles communication with OpenRouter API"""
    
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'Content-Type': 'application/json',
            'Authorization': f'Bearer {API_KEY}'
        })
        self.session.timeout = TIMEOUT
    
    def send_message(self, message: str) -> str:
        """Send a message to the AI and return the response"""
        payload = {
            "model": MODEL,
            "messages": [
                {
                    "role": "user",
                    "content": message
                }
            ]
        }
        
        try:
            response = self.session.post(OPENROUTER_URL, json=payload)
            response.raise_for_status()
            
            data = response.json()
            
            # Check for API errors
            if 'error' in data:
                raise Exception(f"API Error: {data['error'].get('message', 'Unknown error')}")
            
            # Extract the response
            if 'choices' not in data or len(data['choices']) == 0:
                raise Exception("No response choices received from API")
            
            return data['choices'][0]['message']['content']
            
        except requests.exceptions.Timeout:
            raise Exception("Request timed out. Please check your internet connection.")
        except requests.exceptions.ConnectionError:
            raise Exception("Connection error. Please check your internet connection.")
        except requests.exceptions.HTTPError as e:
            raise Exception(f"HTTP Error {e.response.status_code}: {e.response.text}")
        except json.JSONDecodeError:
            raise Exception("Invalid response format from API")
        except Exception as e:
            if "API Error" in str(e):
                raise
            raise Exception(f"Unexpected error: {str(e)}")


class CLI:
    """Command-line interface for the AI assistant"""
    
    def __init__(self):
        self.client = AIClient()
        if RICH_AVAILABLE:
            self.console = Console()
        self.running = True
    
    def print_welcome(self):
        """Display welcome message"""
        if RICH_AVAILABLE:
            welcome_panel = Panel.fit(
                "[bold cyan]Gemini-like AI Assistant[/bold cyan]\n"
                "[dim]Powered by DeepSeek via OpenRouter[/dim]",
                border_style="cyan"
            )
            self.console.print(welcome_panel)
            self.console.print()
            self.console.print("Welcome! Type your questions or prompts below.")
            self.console.print("Commands: [bold]help[/bold], [bold]clear[/bold], [bold]exit[/bold]/[bold]quit[/bold]")
            self.console.print()
        else:
            print("╔══════════════════════════════════════════════════════════════╗")
            print("║                    Gemini-like AI Assistant                 ║")
            print("║                  Powered by DeepSeek via OpenRouter         ║")
            print("╚══════════════════════════════════════════════════════════════╝")
            print()
            print("Welcome! Type your questions or prompts below.")
            print("Commands: help, clear, exit/quit")
            print()
    
    def print_help(self):
        """Display help information"""
        if RICH_AVAILABLE:
            help_text = Text()
            help_text.append("📖 Help:\n", style="bold blue")
            help_text.append("  • Ask any question or provide any prompt\n")
            help_text.append("  • The AI will respond using the DeepSeek model\n")
            help_text.append("  • Type 'exit' or 'quit' to exit\n")
            help_text.append("  • Type 'clear' to clear the screen\n")
            help_text.append("  • Type 'help' to show this help message\n")
            self.console.print(help_text)
        else:
            print("\n📖 Help:")
            print("  • Ask any question or provide any prompt")
            print("  • The AI will respond using the DeepSeek model")
            print("  • Type 'exit' or 'quit' to exit")
            print("  • Type 'clear' to clear the screen")
            print("  • Type 'help' to show this help message")
            print()
    
    def clear_screen(self):
        """Clear the terminal screen"""
        os.system('cls' if os.name == 'nt' else 'clear')
    
    def get_user_input(self) -> str:
        """Get input from user"""
        if RICH_AVAILABLE:
            return Prompt.ask("💬 [bold green]You[/bold green]")
        else:
            return input("💬 You: ").strip()
    
    def show_ai_response(self, response: str):
        """Display AI response"""
        if RICH_AVAILABLE:
            self.console.print(f"🤖 [bold blue]AI[/bold blue]: {response}")
            self.console.print()
        else:
            print(f"🤖 AI: {response}")
            print()
    
    def show_error(self, error: str):
        """Display error message"""
        if RICH_AVAILABLE:
            self.console.print(f"❌ [bold red]Error[/bold red]: {error}")
            self.console.print()
        else:
            print(f"❌ Error: {error}")
            print()
    
    def show_thinking(self):
        """Show thinking indicator"""
        if RICH_AVAILABLE:
            return Spinner("dots", text="🤖 AI: Thinking...")
        else:
            print("🤖 AI: Thinking...", end="", flush=True)
            return None
    
    def handle_command(self, user_input: str) -> bool:
        """Handle special commands. Returns True if command was handled."""
        command = user_input.lower().strip()
        
        if command in ['exit', 'quit']:
            if RICH_AVAILABLE:
                self.console.print("👋 [bold yellow]Goodbye![/bold yellow]")
            else:
                print("👋 Goodbye!")
            self.running = False
            return True
        
        elif command == 'clear':
            self.clear_screen()
            self.print_welcome()
            return True
        
        elif command == 'help':
            self.print_help()
            return True
        
        return False
    
    def run(self):
        """Main application loop"""
        self.print_welcome()
        
        while self.running:
            try:
                # Get user input
                user_input = self.get_user_input()
                
                # Skip empty input
                if not user_input.strip():
                    continue
                
                # Handle commands
                if self.handle_command(user_input):
                    continue
                
                # Show thinking indicator and get AI response
                if RICH_AVAILABLE:
                    with Live(self.show_thinking(), console=self.console, refresh_per_second=10):
                        response = self.client.send_message(user_input)
                else:
                    spinner = self.show_thinking()
                    response = self.client.send_message(user_input)
                    print("\r" + " " * 20 + "\r", end="")  # Clear thinking message
                
                # Display response
                self.show_ai_response(response)
                
            except KeyboardInterrupt:
                if RICH_AVAILABLE:
                    self.console.print("\n👋 [bold yellow]Goodbye![/bold yellow]")
                else:
                    print("\n👋 Goodbye!")
                break
            
            except Exception as e:
                self.show_error(str(e))


def main():
    """Main entry point"""
    try:
        cli = CLI()
        cli.run()
    except KeyboardInterrupt:
        print("\n👋 Goodbye!")
    except Exception as e:
        print(f"❌ Fatal error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
