package main

import (
	"bufio"
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"os"
	"strings"
	"time"
)

const (
	openRouterURL = "https://openrouter.ai/api/v1/chat/completions"
	apiKey        = "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"
	model         = "deepseek/deepseek-chat-v3-0324:free"
)

// OpenRouter API structures
type Message struct {
	Role    string `json:"role"`
	Content string `json:"content"`
}

type ChatRequest struct {
	Model    string    `json:"model"`
	Messages []Message `json:"messages"`
}

type Choice struct {
	Message Message `json:"message"`
}

type ChatResponse struct {
	Choices []Choice `json:"choices"`
	Error   *APIError `json:"error,omitempty"`
}

type APIError struct {
	Message string `json:"message"`
	Type    string `json:"type"`
	Code    string `json:"code"`
}

// AIClient handles communication with OpenRouter API
type AIClient struct {
	httpClient *http.Client
	apiKey     string
}

// NewAIClient creates a new AI client
func NewAIClient() *AIClient {
	return &AIClient{
		httpClient: &http.Client{
			Timeout: 30 * time.Second,
		},
		apiKey: apiKey,
	}
}

// SendMessage sends a message to the AI and returns the response
func (c *AIClient) SendMessage(message string) (string, error) {
	// Create the request payload
	request := ChatRequest{
		Model: model,
		Messages: []Message{
			{
				Role:    "user",
				Content: message,
			},
		},
	}

	// Marshal the request to JSON
	jsonData, err := json.Marshal(request)
	if err != nil {
		return "", fmt.Errorf("failed to marshal request: %v", err)
	}

	// Create HTTP request
	req, err := http.NewRequest("POST", openRouterURL, bytes.NewBuffer(jsonData))
	if err != nil {
		return "", fmt.Errorf("failed to create request: %v", err)
	}

	// Set headers
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer "+c.apiKey)

	// Send the request
	resp, err := c.httpClient.Do(req)
	if err != nil {
		return "", fmt.Errorf("failed to send request: %v", err)
	}
	defer resp.Body.Close()

	// Read the response
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", fmt.Errorf("failed to read response: %v", err)
	}

	// Check for HTTP errors
	if resp.StatusCode != http.StatusOK {
		return "", fmt.Errorf("API request failed with status %d: %s", resp.StatusCode, string(body))
	}

	// Parse the response
	var chatResponse ChatResponse
	if err := json.Unmarshal(body, &chatResponse); err != nil {
		return "", fmt.Errorf("failed to parse response: %v", err)
	}

	// Check for API errors
	if chatResponse.Error != nil {
		return "", fmt.Errorf("API error: %s", chatResponse.Error.Message)
	}

	// Extract the message
	if len(chatResponse.Choices) == 0 {
		return "", fmt.Errorf("no response choices received")
	}

	return chatResponse.Choices[0].Message.Content, nil
}

// CLI handles the command-line interface
type CLI struct {
	client *AIClient
	reader *bufio.Reader
}

// NewCLI creates a new CLI instance
func NewCLI() *CLI {
	return &CLI{
		client: NewAIClient(),
		reader: bufio.NewReader(os.Stdin),
	}
}

// printWelcome displays the welcome message
func (cli *CLI) printWelcome() {
	fmt.Println("╔══════════════════════════════════════════════════════════════╗")
	fmt.Println("║                    Gemini-like AI Assistant                 ║")
	fmt.Println("║                  Powered by DeepSeek via OpenRouter         ║")
	fmt.Println("╚══════════════════════════════════════════════════════════════╝")
	fmt.Println()
	fmt.Println("Welcome! Type your questions or prompts below.")
	fmt.Println("Commands:")
	fmt.Println("  - Type 'exit' or 'quit' to exit the application")
	fmt.Println("  - Type 'clear' to clear the screen")
	fmt.Println("  - Type 'help' to show this help message")
	fmt.Println()
}

// printHelp displays help information
func (cli *CLI) printHelp() {
	fmt.Println("\n📖 Help:")
	fmt.Println("  - Ask any question or provide any prompt")
	fmt.Println("  - The AI will respond using the DeepSeek model")
	fmt.Println("  - Type 'exit' or 'quit' to exit")
	fmt.Println("  - Type 'clear' to clear the screen")
	fmt.Println("  - Type 'help' to show this help message")
	fmt.Println()
}

// clearScreen clears the terminal screen
func (cli *CLI) clearScreen() {
	fmt.Print("\033[2J\033[H")
}

// readInput reads user input from the command line
func (cli *CLI) readInput() (string, error) {
	fmt.Print("💬 You: ")
	input, err := cli.reader.ReadString('\n')
	if err != nil {
		return "", err
	}
	return strings.TrimSpace(input), nil
}

// Run starts the CLI application
func (cli *CLI) Run() {
	cli.printWelcome()

	for {
		input, err := cli.readInput()
		if err != nil {
			fmt.Printf("❌ Error reading input: %v\n", err)
			continue
		}

		// Handle empty input
		if input == "" {
			continue
		}

		// Handle commands
		switch strings.ToLower(input) {
		case "exit", "quit":
			fmt.Println("👋 Goodbye!")
			return
		case "clear":
			cli.clearScreen()
			cli.printWelcome()
			continue
		case "help":
			cli.printHelp()
			continue
		}

		// Show thinking indicator
		fmt.Print("🤖 AI: Thinking...")

		// Send message to AI
		response, err := cli.client.SendMessage(input)
		if err != nil {
			fmt.Printf("\r❌ Error: %v\n\n", err)
			continue
		}

		// Clear thinking indicator and show response
		fmt.Print("\r🤖 AI: ")
		fmt.Println(response)
		fmt.Println()
	}
}

func main() {
	cli := NewCLI()
	cli.Run()
}
