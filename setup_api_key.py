#!/usr/bin/env python3
"""
Setup script for OpenRouter API key
Helps users configure their API key for the Gemini AI CLI
"""

import os
import sys
from pathlib import Path

def setup_api_key():
    """Interactive setup for OpenRouter API key"""
    
    print("🔑 OpenRouter API Key Setup")
    print("=" * 40)
    print()
    print("To use the Gemini AI CLI, you need an OpenRouter API key.")
    print()
    print("📋 Steps to get your API key:")
    print("1. Visit: https://openrouter.ai/")
    print("2. Sign up for a free account")
    print("3. Go to your dashboard")
    print("4. Generate an API key")
    print()
    
    # Get API key from user
    api_key = input("🔐 Enter your OpenRouter API key: ").strip()
    
    if not api_key:
        print("❌ No API key provided. Exiting.")
        return False
    
    if not api_key.startswith('sk-or-v1-'):
        print("⚠️  Warning: API key doesn't look like an OpenRouter key (should start with 'sk-or-v1-')")
        confirm = input("Continue anyway? (y/N): ").lower()
        if confirm != 'y':
            return False
    
    # Choose setup method
    print()
    print("📝 Choose setup method:")
    print("1. Environment variable (recommended)")
    print("2. Config file (~/.openrouter_key)")
    print("3. Show manual instructions")
    
    choice = input("Enter choice (1-3): ").strip()
    
    if choice == "1":
        return setup_environment_variable(api_key)
    elif choice == "2":
        return setup_config_file(api_key)
    elif choice == "3":
        show_manual_instructions(api_key)
        return True
    else:
        print("❌ Invalid choice")
        return False

def setup_environment_variable(api_key):
    """Set up environment variable"""
    print()
    print("🌍 Setting up environment variable...")
    
    if os.name == 'nt':  # Windows
        print("For Windows, run this command in Command Prompt:")
        print(f"set OPENROUTER_API_KEY={api_key}")
        print()
        print("To make it permanent, run:")
        print(f"setx OPENROUTER_API_KEY {api_key}")
        
        # Try to set it for current session
        os.environ['OPENROUTER_API_KEY'] = api_key
        print("✅ Set for current session")
        
    else:  # Unix/Linux/macOS
        shell_profile = None
        home = Path.home()
        
        # Detect shell and profile file
        shell = os.environ.get('SHELL', '')
        if 'bash' in shell:
            shell_profile = home / '.bashrc'
        elif 'zsh' in shell:
            shell_profile = home / '.zshrc'
        else:
            shell_profile = home / '.profile'
        
        print(f"Adding to {shell_profile}...")
        
        try:
            with open(shell_profile, 'a') as f:
                f.write(f"\n# OpenRouter API Key for Gemini AI CLI\n")
                f.write(f"export OPENROUTER_API_KEY='{api_key}'\n")
            
            print(f"✅ Added to {shell_profile}")
            print("Run this command to reload:")
            print(f"source {shell_profile}")
            
        except Exception as e:
            print(f"❌ Failed to write to {shell_profile}: {e}")
            return False
    
    return True

def setup_config_file(api_key):
    """Set up config file"""
    print()
    print("📁 Setting up config file...")
    
    config_file = Path.home() / '.openrouter_key'
    
    try:
        with open(config_file, 'w') as f:
            f.write(api_key)
        
        # Set appropriate permissions (Unix only)
        if os.name != 'nt':
            os.chmod(config_file, 0o600)
        
        print(f"✅ API key saved to {config_file}")
        return True
        
    except Exception as e:
        print(f"❌ Failed to create config file: {e}")
        return False

def show_manual_instructions(api_key):
    """Show manual setup instructions"""
    print()
    print("📋 Manual Setup Instructions")
    print("=" * 30)
    print()
    print("Option 1 - Environment Variable:")
    if os.name == 'nt':
        print(f"set OPENROUTER_API_KEY={api_key}")
        print("Or for permanent:")
        print(f"setx OPENROUTER_API_KEY {api_key}")
    else:
        print(f"export OPENROUTER_API_KEY='{api_key}'")
        print("Add to ~/.bashrc or ~/.zshrc for permanent setup")
    
    print()
    print("Option 2 - Config File:")
    print(f"echo '{api_key}' > ~/.openrouter_key")
    if os.name != 'nt':
        print("chmod 600 ~/.openrouter_key")
    
    print()

def test_api_key():
    """Test if the API key is working"""
    print()
    print("🧪 Testing API key...")
    
    try:
        # Import and test the API
        sys.path.insert(0, '.')
        from gemini_ai_cli import AIClient
        
        client = AIClient()
        response = client.send_message("Hello, please respond with 'API key working' if you can read this.")
        
        print("✅ API key is working!")
        print(f"Response: {response}")
        return True
        
    except Exception as e:
        print(f"❌ API key test failed: {e}")
        return False

def main():
    """Main function"""
    print("🚀 Gemini AI CLI - API Key Setup")
    print()
    
    # Check if API key is already set
    api_key = os.getenv('OPENROUTER_API_KEY')
    config_file = Path.home() / '.openrouter_key'
    
    if api_key:
        print("✅ API key found in environment variable")
        test_choice = input("Test the API key? (Y/n): ").lower()
        if test_choice != 'n':
            test_api_key()
        return
    
    if config_file.exists():
        print("✅ API key found in config file")
        test_choice = input("Test the API key? (Y/n): ").lower()
        if test_choice != 'n':
            test_api_key()
        return
    
    # No API key found, run setup
    print("❌ No API key found. Let's set one up!")
    print()
    
    if setup_api_key():
        print()
        print("🎉 Setup complete!")
        
        test_choice = input("Test the API key now? (Y/n): ").lower()
        if test_choice != 'n':
            test_api_key()
        
        print()
        print("You can now use the Gemini AI CLI:")
        print("python gemini_ai_cli.py")
    else:
        print("❌ Setup failed. Please try again.")

if __name__ == "__main__":
    main()
