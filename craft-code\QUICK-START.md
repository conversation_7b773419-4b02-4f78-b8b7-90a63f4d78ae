# 🚀 Craft Code CLI - Quick Start Guide

Get up and running with Craft Code CLI in under 5 minutes!

## ⚡ Installation (Choose One)

### Windows (Easiest)
```cmd
# Download this repository, then:
install.bat
```

### macOS/Linux
```bash
# Download this repository, then:
chmod +x install.sh
./install.sh
```

### Any Platform
```bash
# Download this repository, then:
python setup.py install
```

## 🎯 First Steps

1. **Launch the application**:
   ```bash
   codecraft
   ```

2. **Complete setup wizard** (first time only):
   - Enter your name
   - Choose programming language
   - Set project directory

3. **Try some commands**:
   ```bash
   # Interactive mode
   codecraft> help
   codecraft> create project MyFirstProject
   codecraft> config list
   codecraft> exit
   
   # Command line mode
   codecraft --version
   codecraft create project WebApp
   codecraft --help
   ```

## 🛠 What You Can Do

- **Create Projects**: `create project <name>`
- **Generate Files**: `create file <name>`
- **Code Templates**: `generate`
- **Configuration**: `config set/get/list`
- **Help**: `help` or `--help`

## 📁 Project Structure

When you create a project, you get:
```
MyProject/
├── src/          # Source code
├── tests/        # Test files
├── docs/         # Documentation
├── README.md     # Project description
└── .gitignore    # Git ignore rules
```

## 🎉 You're Ready!

That's it! You now have a powerful CLI tool for managing your development projects.

**Next Steps:**
- Read the [User Guide](docs/USER_GUIDE.md)
- Check out [Installation Details](INSTALLATION.md)
- Explore the [GitHub Repository](https://github.com/CodeCraft/craft-code-cli)

**Need Help?**
- Type `codecraft --help`
- Visit our [Documentation](docs/)
- Report issues on [GitHub](https://github.com/CodeCraft/craft-code-cli/issues)

---

**Happy Coding! 🎨**
