@echo off
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    Gemini AI CLI Builder                    ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

REM Check if Go is installed
go version >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo ❌ Go is not installed on this system.
    echo.
    echo Please choose one of the following options:
    echo.
    echo 1. Install Go automatically using PowerShell:
    echo    powershell -ExecutionPolicy Bypass -File install-and-build.ps1
    echo.
    echo 2. Install Go manually:
    echo    - Visit: https://golang.org/dl/
    echo    - Download and install Go for Windows
    echo    - Restart this script after installation
    echo.
    echo 3. Use the pre-built executable (if available):
    echo    - Look for gemini-ai.exe in this folder
    echo    - Double-click to run
    echo.
    pause
    exit /b 1
)

echo ✅ Go is installed. Building application...
echo.

REM Set environment variables for Windows build
set GOOS=windows
set GOARCH=amd64
set CGO_ENABLED=0

REM Build the executable
echo 🔨 Compiling...
go build -ldflags="-s -w" -o gemini-ai.exe main.go

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✅ Build successful!
    echo 📁 Executable created: gemini-ai.exe
    echo 📊 File size:
    for %%A in (gemini-ai.exe) do echo    %%~zA bytes
    echo.
    echo 🎉 Ready to use! Run the application with:
    echo    .\gemini-ai.exe
    echo.
    echo    or double-click the gemini-ai.exe file
    echo.
) else (
    echo.
    echo ❌ Build failed!
    echo Please check for errors above.
    echo.
    echo If you continue to have issues:
    echo 1. Ensure Go is properly installed
    echo 2. Check your internet connection
    echo 3. Try running as administrator
    echo.
)

pause
