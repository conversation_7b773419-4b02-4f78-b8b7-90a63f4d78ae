@echo off
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    Gemini AI CLI Builder                    ║
echo ║                      Python Version                         ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

REM Check if Python is installed
python --version >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo ❌ Python is not installed on this system.
    echo.
    echo Please install Python first:
    echo 1. Visit: https://www.python.org/downloads/
    echo 2. Download Python 3.8 or newer for Windows
    echo 3. During installation, check "Add Python to PATH"
    echo 4. Restart this script after installation
    echo.
    pause
    exit /b 1
)

echo ✅ Python is installed.
python --version

REM Check if pip is available
pip --version >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo ❌ pip is not available.
    echo Please ensure pip is installed with Python.
    pause
    exit /b 1
)

echo ✅ pip is available.
echo.

REM Install dependencies
echo 🔧 Installing dependencies...
pip install -r requirements.txt

if %ERRORLEVEL% NEQ 0 (
    echo ❌ Failed to install dependencies.
    echo.
    echo Try running as administrator or check your internet connection.
    pause
    exit /b 1
)

echo ✅ Dependencies installed successfully.
echo.

REM Build the executable
echo 🔨 Building standalone executable...
echo This may take a few minutes...
echo.

pyinstaller --onefile --console --name "gemini-ai" gemini_ai_cli.py

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✅ Build successful!
    echo 📁 Executable created: dist\gemini-ai.exe
    echo 📊 File location: %CD%\dist\gemini-ai.exe
    echo.
    echo 🎉 Ready to use! Run the application with:
    echo    dist\gemini-ai.exe
    echo.
    echo    or copy gemini-ai.exe to any Windows computer and run it
    echo.
) else (
    echo.
    echo ❌ Build failed!
    echo Please check for errors above.
    echo.
    echo Common solutions:
    echo 1. Ensure all dependencies are installed
    echo 2. Try running as administrator
    echo 3. Check antivirus settings (may block PyInstaller)
    echo.
)

pause
