# 🚀 Craft Code CLI - by CodeCraft

A powerful command-line interface tool for developers to streamline coding workflows, project management, and development tasks.

## ⚡ TL;DR - Quick Install

**Windows:** Open Command Prompt and run:
```cmd
git clone https://github.com/im-rahulr/craftcode.git && cd craftcode/craft-code && install.bat
```

**macOS/Linux:** Open Terminal and run:
```bash
git clone https://github.com/im-rahulr/craftcode.git && cd craftcode/craft-code && chmod +x install.sh && ./install.sh
```

Then use `codecraft` from anywhere! 🎉

## 📥 Installation Guide

### 🪟 Windows Installation (Easiest)

1. **Download the repository**:
   ```cmd
   git clone https://github.com/im-rahulr/craftcode.git
   cd craftcode/craft-code
   ```

   *Or download ZIP from GitHub and extract to a folder*

2. **Run the automatic installer**:
   ```cmd
   install.bat
   ```

   *The installer will:*
   - ✅ Check if Python is installed
   - ✅ Download and install Python if needed
   - ✅ Install required dependencies
   - ✅ Create global `codecraft` command

3. **Start using Craft Code CLI**:
   ```cmd
   codecraft
   ```

### 🐧 Linux / 🍎 macOS Installation

1. **Download the repository**:
   ```bash
   git clone https://github.com/im-rahulr/craftcode.git
   cd craftcode/craft-code
   ```

2. **Run the installer**:
   ```bash
   chmod +x install.sh
   ./install.sh
   ```

3. **Start using Craft Code CLI**:
   ```bash
   codecraft
   ```

### 🌐 Universal Installation (Any Platform)

1. **Download the repository**:
   ```bash
   git clone https://github.com/im-rahulr/craftcode.git
   cd craftcode/craft-code
   ```

2. **Run Python installer**:
   ```bash
   python setup.py install
   ```

3. **Use the global command**:
   ```bash
   codecraft
   ```

## ❓ Installation Issues?

### 🔧 Common Solutions

**"Python not found" error:**
- Windows: The installer will automatically download and install Python
- macOS: Install Homebrew first: `brew install python@3.11`
- Linux: Use your package manager: `sudo apt install python3 python3-pip`

**"codecraft command not found":**
- Restart your terminal/command prompt
- Or run directly: `python craft-code/src/main.py`

**Permission errors (Linux/macOS):**
- Try with sudo: `sudo ./install.sh`
- Or install to user directory (automatic)

## ✨ What You Get After Installation

After successful installation, you can use `codecraft` from anywhere:

```bash
# Launch interactive mode
codecraft

# Show help
codecraft --help

# Create a new project
codecraft create project MyAwesomeApp

# Show version
codecraft --version
```

## 🎯 Features

- 🛠 **Project Management**: Create, manage, and organize coding projects
- 📝 **Code Generation**: Generate boilerplate code for various frameworks
- 🔧 **Development Tools**: Integrated tools for common development tasks
- 📊 **Analytics**: Track your coding productivity and project metrics
- 🎨 **Customization**: Personalize your development workflow
- 🌐 **Multi-Language Support**: Works with Python, JavaScript, Java, C++, and more

## 📋 System Requirements

- **Operating System**: Windows 10/11, macOS 10.14+, or Linux
- **Python**: 3.8+ (automatically installed if missing)
- **Memory**: 100MB RAM
- **Storage**: 50MB free space
- **Internet**: Required for initial setup only

## 🎮 Usage Examples

### Interactive Mode
```bash
codecraft
```
This launches the interactive CLI where you can:
```
🎯 codecraft> help                    # Show all commands
🎯 codecraft> create project WebApp   # Create a new project
🎯 codecraft> create file main.py     # Create a new file
🎯 codecraft> config list             # Show settings
� codecraft> exit                    # Exit application
```

### Command Line Mode
```bash
# Show version
codecraft --version

# Show help
codecraft --help

# Create project directly
codecraft create project MyApp

# Configure settings
codecraft config set user_name "Your Name"
```

### Project Structure Created
When you create a project, you get:
```
MyProject/
├── src/          # Source code
├── tests/        # Test files
├── docs/         # Documentation
├── README.md     # Project description
└── .gitignore    # Git ignore rules
```

## 🆘 Need Help?

### 📖 Documentation
- **Quick Start**: [QUICK-START.md](QUICK-START.md)
- **Detailed Installation**: [INSTALLATION.md](INSTALLATION.md)
- **User Guide**: [docs/USER_GUIDE.md](docs/USER_GUIDE.md)

### 🐛 Having Issues?
1. **Check the troubleshooting section** above
2. **Restart your terminal** after installation
3. **Try running as administrator** (Windows) or with `sudo` (Linux/macOS)
4. **Report issues**: [GitHub Issues](https://github.com/im-rahulr/craftcode/issues)

### � Quick Troubleshooting
```bash
# If codecraft command doesn't work, try:
python craft-code/src/main.py

# Check if Python is installed:
python --version

# Check if dependencies are installed:
pip list | grep rich
```

## 🔧 For Developers

Want to contribute or modify the code?

```bash
# Clone the repository
git clone https://github.com/im-rahulr/craftcode.git
cd craftcode/craft-code

# Install in development mode
pip install -e .

# Run the application
python src/main.py
```

## 🌟 What Makes This Special?

- ✅ **Zero Setup Hassle**: Automatic Python installation
- ✅ **Works Everywhere**: Windows, macOS, Linux support
- ✅ **Global Access**: `codecraft` command from any directory
- ✅ **Beautiful Interface**: Rich CLI with colors and tables
- ✅ **Smart Project Creation**: Proper directory structures
- ✅ **User Friendly**: Clear error messages and help

## � Ready to Start?

1. **Copy this command** for your operating system:

   **Windows:**
   ```cmd
   git clone https://github.com/im-rahulr/craftcode.git && cd craftcode/craft-code && install.bat
   ```

   **macOS/Linux:**
   ```bash
   git clone https://github.com/im-rahulr/craftcode.git && cd craftcode/craft-code && chmod +x install.sh && ./install.sh
   ```

2. **Paste and run** in your terminal

3. **Start coding** with `codecraft`!

## �📄 License

MIT License - see [LICENSE](LICENSE) file for details.

---

**Made with ❤️ by CodeCraft Team**

⭐ **Star this repository** if you find it helpful!
