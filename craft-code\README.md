# 🚀 Craft Code CLI - by CodeCraft

A powerful command-line interface tool for developers to streamline coding workflows, project management, and development tasks.

## ⚡ Quick Installation

### Windows (Recommended)
1. **Download** this repository (Clone or Download ZIP)
2. **Run** the installer:
   ```cmd
   install.bat
   ```
3. **Use** the global command:
   ```cmd
   codecraft
   ```

### Cross-Platform
1. **Download** this repository
2. **Run** the setup script:
   ```bash
   python setup.py install
   ```
3. **Use** the global command:
   ```bash
   codecraft
   ```

## 🎯 Features

- 🛠 **Project Management**: Create, manage, and organize coding projects
- 📝 **Code Generation**: Generate boilerplate code for various frameworks
- 🔧 **Development Tools**: Integrated tools for common development tasks
- 📊 **Analytics**: Track your coding productivity and project metrics
- 🎨 **Customization**: Personalize your development workflow
- 🌐 **Multi-Language Support**: Works with Python, JavaScript, Java, C++, and more

## 📋 Requirements

- **Windows 10/11** (Primary support)
- **Python 3.8+** (automatically installed if missing)
- **Internet connection** (for initial setup)

## 🚀 Installation Details

The installation process will:
1. ✅ Check if Python is installed
2. ✅ Download and install Python if missing
3. ✅ Install required dependencies
4. ✅ Create global `codecraft` command
5. ✅ Set up PATH configuration
6. ✅ Verify installation

## 🎮 Usage

After installation, use these commands:

```cmd
# Launch Craft Code CLI
codecraft

# Show help
codecraft --help

# Create new project
codecraft create project

# Generate code templates
codecraft generate

# Show version
codecraft --version
```

## 📁 Project Structure

```
craft-code/
├── 📄 README.md              # This file
├── 📄 install.bat            # Windows installer
├── 📄 setup.py               # Cross-platform installer
├── 📄 requirements.txt       # Python dependencies
├── 📁 src/                   # Source code
│   ├── 📄 __init__.py
│   ├── 📄 main.py            # Main CLI application
│   ├── 📄 cli.py             # CLI interface
│   └── 📁 modules/           # Feature modules
├── 📁 scripts/               # Installation scripts
├── 📁 docs/                  # Documentation
└── 📁 templates/             # Code templates
```

## 🔧 Development

To contribute or modify:

```bash
# Clone the repository
git clone https://github.com/CodeCraft/craft-code-cli.git

# Install in development mode
python setup.py develop

# Run tests
python -m pytest tests/
```

## 📞 Support

- 📖 **Documentation**: [docs/](docs/)
- 🐛 **Issues**: [GitHub Issues](https://github.com/CodeCraft/craft-code-cli/issues)
- 💬 **Discussions**: [GitHub Discussions](https://github.com/CodeCraft/craft-code-cli/discussions)

## 📄 License

MIT License - see [LICENSE](LICENSE) file for details.

---

**Made with ❤️ by CodeCraft Team**
