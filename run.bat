@echo off
title Gemini AI CLI Assistant

REM Check if the Python executable exists
if exist "dist\gemini-ai.exe" (
    echo 🚀 Starting Gemini AI CLI Assistant (Standalone)...
    echo.
    dist\gemini-ai.exe
    goto :end
)

REM Check if Python script exists
if exist "gemini_ai_cli.py" (
    echo 🚀 Starting Gemini AI CLI Assistant (Python)...
    echo.
    python gemini_ai_cli.py
    goto :end
)

REM Neither found
echo ❌ Application not found!
echo.
echo Please build the application first:
echo   1. Run build.bat to create standalone executable, or
echo   2. Ensure gemini_ai_cli.py exists for Python version
echo.
pause
exit /b 1

:end
REM Keep window open if there was an error
if %ERRORLEVEL% NEQ 0 (
    echo.
    echo ❌ Application exited with an error.
    echo.
    pause
)
