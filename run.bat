@echo off
title Gemini AI CLI Assistant

REM Check if the executable exists
if not exist "gemini-ai.exe" (
    echo ❌ gemini-ai.exe not found!
    echo.
    echo Please build the application first:
    echo   1. Run build.bat, or
    echo   2. Run install-and-build.ps1
    echo.
    pause
    exit /b 1
)

REM Run the application
echo 🚀 Starting Gemini AI CLI Assistant...
echo.
gemini-ai.exe

REM Keep window open if there was an error
if %ERRORLEVEL% NEQ 0 (
    echo.
    echo ❌ Application exited with an error.
    echo.
    pause
)
