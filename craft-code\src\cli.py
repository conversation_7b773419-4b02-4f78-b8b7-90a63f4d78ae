"""
Craft Code CLI - Command Line Interface
Handles all CLI interactions and commands
"""

import os
import sys
import json
import time
from pathlib import Path
from datetime import datetime

try:
    from rich.console import Console
    from rich.panel import Panel
    from rich.text import Text
    from rich.prompt import Prompt, Confirm
    from rich.table import Table
    from rich.progress import Progress, SpinnerColumn, TextColumn
    RICH_AVAILABLE = True
except ImportError:
    RICH_AVAILABLE = False

class CraftCodeCLI:
    """Main CLI class for Craft Code"""
    
    def __init__(self):
        self.console = Console() if RICH_AVAILABLE else None
        self.config_dir = Path.home() / '.codecraft'
        self.config_file = self.config_dir / 'config.json'
        self.ensure_config_dir()
        self.config = self.load_config()
    
    def ensure_config_dir(self):
        """Ensure configuration directory exists"""
        self.config_dir.mkdir(exist_ok=True)
    
    def load_config(self):
        """Load configuration from file"""
        if self.config_file.exists():
            try:
                with open(self.config_file, 'r') as f:
                    return json.load(f)
            except:
                pass
        return {
            'user_name': '',
            'default_language': 'python',
            'project_directory': str(Path.home() / 'CodeCraft-Projects'),
            'theme': 'default'
        }
    
    def save_config(self):
        """Save configuration to file"""
        with open(self.config_file, 'w') as f:
            json.dump(self.config, f, indent=2)
    
    def print_welcome(self):
        """Display welcome message"""
        if RICH_AVAILABLE:
            welcome_panel = Panel.fit(
                "[bold cyan]🚀 Craft Code CLI[/bold cyan]\n"
                "[dim]by CodeCraft Team[/dim]\n\n"
                "[green]Your powerful development companion[/green]",
                border_style="cyan"
            )
            self.console.print(welcome_panel)
            self.console.print()
        else:
            print("🚀 Craft Code CLI - by CodeCraft Team")
            print("Your powerful development companion")
            print("=" * 50)
    
    def print_help(self):
        """Display help information"""
        if RICH_AVAILABLE:
            help_table = Table(title="Available Commands")
            help_table.add_column("Command", style="cyan", no_wrap=True)
            help_table.add_column("Description", style="white")
            help_table.add_column("Example", style="dim")
            
            help_table.add_row("create project", "Create a new project", "codecraft create project MyApp")
            help_table.add_row("create file", "Create a new file", "codecraft create file main.py")
            help_table.add_row("generate", "Generate code templates", "codecraft generate")
            help_table.add_row("config", "Configure settings", "codecraft config set user_name John")
            help_table.add_row("help", "Show this help", "codecraft help")
            help_table.add_row("exit", "Exit the application", "exit")
            
            self.console.print(help_table)
        else:
            print("\n📖 Available Commands:")
            print("  create project <name>  - Create a new project")
            print("  create file <name>     - Create a new file")
            print("  generate               - Generate code templates")
            print("  config <action>        - Configure settings")
            print("  help                   - Show this help")
            print("  exit                   - Exit the application")
    
    def run_interactive(self):
        """Run interactive CLI mode"""
        self.print_welcome()
        
        # First-time setup
        if not self.config.get('user_name'):
            self.setup_first_time()
        
        if RICH_AVAILABLE:
            self.console.print(f"👋 Welcome back, [bold green]{self.config.get('user_name', 'Developer')}[/bold green]!")
            self.console.print("Type [bold cyan]help[/bold cyan] for available commands or [bold red]exit[/bold red] to quit.\n")
        else:
            print(f"👋 Welcome back, {self.config.get('user_name', 'Developer')}!")
            print("Type 'help' for available commands or 'exit' to quit.\n")
        
        while True:
            try:
                if RICH_AVAILABLE:
                    command = Prompt.ask("🎯 [bold cyan]codecraft[/bold cyan]")
                else:
                    command = input("🎯 codecraft> ").strip()
                
                if not command:
                    continue
                
                parts = command.split()
                cmd = parts[0].lower()
                
                if cmd in ['exit', 'quit']:
                    if RICH_AVAILABLE:
                        self.console.print("👋 [bold yellow]Happy coding![/bold yellow]")
                    else:
                        print("👋 Happy coding!")
                    break
                elif cmd == 'help':
                    self.print_help()
                elif cmd == 'create':
                    if len(parts) >= 2:
                        item_type = parts[1]
                        name = parts[2] if len(parts) > 2 else None
                        self.handle_create(item_type, name)
                    else:
                        self.print_error("Usage: create <project|file> [name]")
                elif cmd == 'generate':
                    template = parts[1] if len(parts) > 1 else None
                    self.handle_generate(template)
                elif cmd == 'config':
                    if len(parts) >= 2:
                        action = parts[1]
                        key = parts[2] if len(parts) > 2 else None
                        value = parts[3] if len(parts) > 3 else None
                        self.handle_config(action, key, value)
                    else:
                        self.print_error("Usage: config <set|get|list> [key] [value]")
                else:
                    self.print_error(f"Unknown command: {cmd}. Type 'help' for available commands.")
                
                print()  # Add spacing between commands
                
            except KeyboardInterrupt:
                if RICH_AVAILABLE:
                    self.console.print("\n👋 [bold yellow]Happy coding![/bold yellow]")
                else:
                    print("\n👋 Happy coding!")
                break
            except EOFError:
                break
    
    def setup_first_time(self):
        """Setup wizard for first-time users"""
        if RICH_AVAILABLE:
            self.console.print("🎉 [bold green]Welcome to Craft Code CLI![/bold green]")
            self.console.print("Let's set up your development environment.\n")
            
            name = Prompt.ask("What's your name?", default="Developer")
            lang = Prompt.ask("Preferred programming language?", 
                            choices=["python", "javascript", "java", "cpp", "other"], 
                            default="python")
            project_dir = Prompt.ask("Default project directory?", 
                                   default=str(Path.home() / "CodeCraft-Projects"))
        else:
            print("🎉 Welcome to Craft Code CLI!")
            print("Let's set up your development environment.\n")
            
            name = input("What's your name? (Developer): ").strip() or "Developer"
            lang = input("Preferred programming language? (python): ").strip() or "python"
            project_dir = input(f"Default project directory? ({Path.home() / 'CodeCraft-Projects'}): ").strip()
            if not project_dir:
                project_dir = str(Path.home() / "CodeCraft-Projects")
        
        self.config.update({
            'user_name': name,
            'default_language': lang,
            'project_directory': project_dir
        })
        self.save_config()
        
        # Create project directory
        Path(project_dir).mkdir(parents=True, exist_ok=True)
        
        if RICH_AVAILABLE:
            self.console.print(f"✅ Setup complete! Your projects will be saved to: [bold blue]{project_dir}[/bold blue]\n")
        else:
            print(f"✅ Setup complete! Your projects will be saved to: {project_dir}\n")
    
    def handle_create(self, item_type, name):
        """Handle create commands"""
        if item_type == 'project':
            self.create_project(name)
        elif item_type == 'file':
            self.create_file(name)
        else:
            self.print_error(f"Unknown create type: {item_type}")
    
    def create_project(self, name):
        """Create a new project"""
        if not name:
            if RICH_AVAILABLE:
                name = Prompt.ask("Project name")
            else:
                name = input("Project name: ").strip()
        
        if not name:
            self.print_error("Project name is required")
            return
        
        project_dir = Path(self.config['project_directory']) / name
        
        if project_dir.exists():
            self.print_error(f"Project '{name}' already exists")
            return
        
        try:
            # Create project structure
            project_dir.mkdir(parents=True)
            (project_dir / 'src').mkdir()
            (project_dir / 'tests').mkdir()
            (project_dir / 'docs').mkdir()
            
            # Create basic files
            readme_content = f"# {name}\n\nA project created with Craft Code CLI.\n\n## Getting Started\n\nTODO: Add project description and setup instructions.\n"
            (project_dir / 'README.md').write_text(readme_content)
            
            gitignore_content = "*.pyc\n__pycache__/\n.env\n.venv/\nnode_modules/\n.DS_Store\n"
            (project_dir / '.gitignore').write_text(gitignore_content)
            
            if RICH_AVAILABLE:
                self.console.print(f"✅ Project '[bold green]{name}[/bold green]' created successfully!")
                self.console.print(f"📁 Location: [blue]{project_dir}[/blue]")
            else:
                print(f"✅ Project '{name}' created successfully!")
                print(f"📁 Location: {project_dir}")
                
        except Exception as e:
            self.print_error(f"Failed to create project: {e}")
    
    def create_file(self, name):
        """Create a new file"""
        if not name:
            if RICH_AVAILABLE:
                name = Prompt.ask("File name")
            else:
                name = input("File name: ").strip()
        
        if not name:
            self.print_error("File name is required")
            return
        
        try:
            file_path = Path(name)
            if file_path.exists():
                if RICH_AVAILABLE:
                    overwrite = Confirm.ask(f"File '{name}' exists. Overwrite?")
                else:
                    overwrite = input(f"File '{name}' exists. Overwrite? (y/N): ").lower().startswith('y')
                
                if not overwrite:
                    return
            
            # Create basic file content based on extension
            content = self.get_file_template(file_path.suffix)
            file_path.write_text(content)
            
            if RICH_AVAILABLE:
                self.console.print(f"✅ File '[bold green]{name}[/bold green]' created successfully!")
            else:
                print(f"✅ File '{name}' created successfully!")
                
        except Exception as e:
            self.print_error(f"Failed to create file: {e}")
    
    def get_file_template(self, extension):
        """Get template content for file based on extension"""
        templates = {
            '.py': '#!/usr/bin/env python3\n"""\nModule description\n"""\n\ndef main():\n    """Main function"""\n    pass\n\nif __name__ == "__main__":\n    main()\n',
            '.js': '/**\n * Module description\n */\n\nfunction main() {\n    // TODO: Implement\n}\n\nif (require.main === module) {\n    main();\n}\n',
            '.java': 'public class Main {\n    public static void main(String[] args) {\n        // TODO: Implement\n    }\n}\n',
            '.cpp': '#include <iostream>\n\nint main() {\n    // TODO: Implement\n    return 0;\n}\n'
        }
        return templates.get(extension, f'// File created with Craft Code CLI\n// {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}\n\n')
    
    def handle_generate(self, template):
        """Handle generate commands"""
        if RICH_AVAILABLE:
            self.console.print("🎨 [bold cyan]Code Generation[/bold cyan]")
            self.console.print("Available templates:")
            self.console.print("  • [green]flask-app[/green] - Flask web application")
            self.console.print("  • [green]fastapi-app[/green] - FastAPI application")
            self.console.print("  • [green]react-component[/green] - React component")
            self.console.print("  • [green]python-class[/green] - Python class template")
        else:
            print("🎨 Code Generation")
            print("Available templates:")
            print("  • flask-app - Flask web application")
            print("  • fastapi-app - FastAPI application")
            print("  • react-component - React component")
            print("  • python-class - Python class template")
    
    def handle_config(self, action, key, value):
        """Handle configuration commands"""
        if action == 'list':
            if RICH_AVAILABLE:
                config_table = Table(title="Configuration")
                config_table.add_column("Key", style="cyan")
                config_table.add_column("Value", style="white")
                
                for k, v in self.config.items():
                    config_table.add_row(k, str(v))
                
                self.console.print(config_table)
            else:
                print("Configuration:")
                for k, v in self.config.items():
                    print(f"  {k}: {v}")
        
        elif action == 'get':
            if key in self.config:
                print(f"{key}: {self.config[key]}")
            else:
                self.print_error(f"Configuration key '{key}' not found")
        
        elif action == 'set':
            if key and value:
                self.config[key] = value
                self.save_config()
                if RICH_AVAILABLE:
                    self.console.print(f"✅ Set [cyan]{key}[/cyan] = [green]{value}[/green]")
                else:
                    print(f"✅ Set {key} = {value}")
            else:
                self.print_error("Usage: config set <key> <value>")
    
    def print_error(self, message):
        """Print error message"""
        if RICH_AVAILABLE:
            self.console.print(f"❌ [bold red]Error:[/bold red] {message}")
        else:
            print(f"❌ Error: {message}")
    
    def print_success(self, message):
        """Print success message"""
        if RICH_AVAILABLE:
            self.console.print(f"✅ [bold green]{message}[/bold green]")
        else:
            print(f"✅ {message}")
