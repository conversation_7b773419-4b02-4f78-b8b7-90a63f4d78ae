#!/usr/bin/env python3
"""
Craft Code CLI - Global Entry Point
This file can be used as an alternative entry point for the global command
"""

import sys
import os
from pathlib import Path

# Add the src directory to Python path
current_dir = Path(__file__).parent
src_dir = current_dir / "src"
sys.path.insert(0, str(src_dir))

# Import and run the main application
try:
    from main import main
    
    if __name__ == "__main__":
        main()
        
except ImportError as e:
    print(f"❌ Error importing Craft Code CLI: {e}")
    print(f"📁 Current directory: {current_dir}")
    print(f"📁 Source directory: {src_dir}")
    print("Please ensure the installation is complete and try again.")
    sys.exit(1)
except Exception as e:
    print(f"❌ Error running Craft Code CLI: {e}")
    sys.exit(1)
