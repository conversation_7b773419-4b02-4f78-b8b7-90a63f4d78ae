#!/usr/bin/env python3
"""
Craft Code CLI - Cross-Platform Setup Script
Handles installation on Windows, macOS, and Linux
"""

import os
import sys
import subprocess
import platform
import shutil
import urllib.request
from pathlib import Path

class CraftCodeInstaller:
    """Cross-platform installer for Craft Code CLI"""
    
    def __init__(self):
        self.system = platform.system().lower()
        self.install_dir = Path(__file__).parent.absolute()
        self.python_min_version = (3, 8)
        
    def print_header(self):
        """Print installation header"""
        print("=" * 80)
        print("🚀 Craft Code CLI - Cross-Platform Installer")
        print("   by CodeCraft Team")
        print("=" * 80)
        print()
        
    def check_python(self):
        """Check if Python is installed and meets minimum version"""
        print("[1/5] 🔍 Checking Python installation...")
        
        try:
            version = sys.version_info
            if version >= self.python_min_version:
                print(f"✅ Python {version.major}.{version.minor}.{version.micro} found")
                return True
            else:
                print(f"❌ Python {version.major}.{version.minor} found, but {self.python_min_version[0]}.{self.python_min_version[1]}+ required")
                return False
        except:
            print("❌ Python not found")
            return False
    
    def install_python(self):
        """Install Python based on the operating system"""
        print("\n[2/5] 📥 Installing Python...")
        
        if self.system == "windows":
            return self.install_python_windows()
        elif self.system == "darwin":  # macOS
            return self.install_python_macos()
        elif self.system == "linux":
            return self.install_python_linux()
        else:
            print(f"❌ Unsupported operating system: {self.system}")
            return False
    
    def install_python_windows(self):
        """Install Python on Windows"""
        print("📡 Downloading Python for Windows...")
        
        try:
            # Download Python installer
            url = "https://www.python.org/ftp/python/3.11.9/python-3.11.9-amd64.exe"
            installer_path = Path.home() / "python-installer.exe"
            
            urllib.request.urlretrieve(url, installer_path)
            print("✅ Python installer downloaded")
            
            print("🚀 Running Python installer...")
            print("⚠️  Please make sure to check 'Add Python to PATH' during installation!")
            
            # Run installer
            subprocess.run([str(installer_path), "/quiet", "InstallAllUsers=1", "PrependPath=1"], check=True)
            
            # Clean up
            installer_path.unlink()
            
            print("✅ Python installed successfully")
            return True
            
        except Exception as e:
            print(f"❌ Failed to install Python: {e}")
            print("Please install Python manually from https://www.python.org/downloads/")
            return False
    
    def install_python_macos(self):
        """Install Python on macOS"""
        print("🍎 Installing Python on macOS...")
        
        # Check if Homebrew is available
        if shutil.which("brew"):
            try:
                subprocess.run(["brew", "install", "python@3.11"], check=True)
                print("✅ Python installed via Homebrew")
                return True
            except subprocess.CalledProcessError:
                pass
        
        print("❌ Please install Python manually:")
        print("   1. Install Homebrew: https://brew.sh/")
        print("   2. Run: brew install python@3.11")
        print("   Or download from: https://www.python.org/downloads/")
        return False
    
    def install_python_linux(self):
        """Install Python on Linux"""
        print("🐧 Installing Python on Linux...")
        
        # Try different package managers
        package_managers = [
            (["apt", "update"], ["apt", "install", "-y", "python3", "python3-pip"]),
            (["yum", "update"], ["yum", "install", "-y", "python3", "python3-pip"]),
            (["dnf", "update"], ["dnf", "install", "-y", "python3", "python3-pip"]),
            (["pacman", "-Sy"], ["pacman", "-S", "--noconfirm", "python", "python-pip"])
        ]
        
        for update_cmd, install_cmd in package_managers:
            if shutil.which(update_cmd[0]):
                try:
                    subprocess.run(update_cmd, check=True, capture_output=True)
                    subprocess.run(install_cmd, check=True)
                    print(f"✅ Python installed via {update_cmd[0]}")
                    return True
                except subprocess.CalledProcessError:
                    continue
        
        print("❌ Could not install Python automatically")
        print("Please install Python manually using your distribution's package manager")
        return False
    
    def install_dependencies(self):
        """Install Python dependencies"""
        print("\n[3/5] 📦 Installing dependencies...")
        
        try:
            # Upgrade pip
            subprocess.run([sys.executable, "-m", "pip", "install", "--upgrade", "pip"], 
                         check=True, capture_output=True)
            
            # Install required packages
            packages = ["rich"]
            for package in packages:
                subprocess.run([sys.executable, "-m", "pip", "install", package], 
                             check=True, capture_output=True)
                print(f"✅ Installed {package}")
            
            return True
            
        except subprocess.CalledProcessError as e:
            print(f"❌ Failed to install dependencies: {e}")
            return False
    
    def create_global_command(self):
        """Create global codecraft command"""
        print("\n[4/5] 🌐 Creating global 'codecraft' command...")
        
        if self.system == "windows":
            return self.create_windows_command()
        else:
            return self.create_unix_command()
    
    def create_windows_command(self):
        """Create Windows batch file for global command"""
        try:
            # Find Python Scripts directory
            scripts_dir = Path(sys.prefix) / "Scripts"
            scripts_dir.mkdir(exist_ok=True)
            
            # Create batch file
            batch_file = scripts_dir / "codecraft.bat"
            batch_content = f'@echo off\npython "{self.install_dir / "src" / "main.py"}" %*\n'
            
            batch_file.write_text(batch_content)
            print(f"✅ Created global command: {batch_file}")
            return True
            
        except Exception as e:
            print(f"❌ Failed to create global command: {e}")
            return False
    
    def create_unix_command(self):
        """Create Unix shell script for global command"""
        try:
            # Try different bin directories
            bin_dirs = [
                Path.home() / ".local" / "bin",
                Path("/usr/local/bin"),
                Path("/usr/bin")
            ]
            
            for bin_dir in bin_dirs:
                if bin_dir.exists() or bin_dir == bin_dirs[0]:  # Create .local/bin if it doesn't exist
                    bin_dir.mkdir(parents=True, exist_ok=True)
                    
                    script_file = bin_dir / "codecraft"
                    script_content = f'#!/bin/bash\npython3 "{self.install_dir / "src" / "main.py"}" "$@"\n'
                    
                    try:
                        script_file.write_text(script_content)
                        script_file.chmod(0o755)  # Make executable
                        print(f"✅ Created global command: {script_file}")
                        
                        # Add to PATH if needed
                        if bin_dir == bin_dirs[0]:  # ~/.local/bin
                            self.add_to_path_unix(str(bin_dir))
                        
                        return True
                    except PermissionError:
                        continue
            
            print("❌ Could not create global command (permission denied)")
            print("Try running with sudo or install manually")
            return False
            
        except Exception as e:
            print(f"❌ Failed to create global command: {e}")
            return False
    
    def add_to_path_unix(self, bin_dir):
        """Add directory to PATH in shell profile"""
        shell_profiles = [
            Path.home() / ".bashrc",
            Path.home() / ".zshrc",
            Path.home() / ".profile"
        ]
        
        path_line = f'export PATH="$PATH:{bin_dir}"\n'
        
        for profile in shell_profiles:
            if profile.exists():
                try:
                    content = profile.read_text()
                    if bin_dir not in content:
                        with profile.open("a") as f:
                            f.write(f"\n# Added by Craft Code CLI installer\n{path_line}")
                        print(f"✅ Added to PATH in {profile}")
                except:
                    pass
    
    def test_installation(self):
        """Test the installation"""
        print("\n[5/5] 🧪 Testing installation...")
        
        try:
            # Test importing the module
            sys.path.insert(0, str(self.install_dir / "src"))
            import main
            print("✅ Module import test passed")
            
            # Test command execution
            result = subprocess.run([sys.executable, str(self.install_dir / "src" / "main.py"), "--version"], 
                                  capture_output=True, text=True, timeout=10)
            
            if result.returncode == 0:
                print("✅ Command execution test passed")
                return True
            else:
                print("⚠️  Command execution test failed")
                return False
                
        except Exception as e:
            print(f"⚠️  Installation test failed: {e}")
            return False
    
    def print_completion(self):
        """Print installation completion message"""
        print("\n" + "=" * 80)
        print("🎉 Installation Complete!")
        print("=" * 80)
        print()
        print("✅ Craft Code CLI has been installed successfully!")
        print()
        print("🚀 Usage:")
        print("   codecraft              # Launch interactive mode")
        print("   codecraft --help       # Show help")
        print("   codecraft --version    # Show version")
        print()
        print(f"📁 Installation location: {self.install_dir}")
        print()
        
        if self.system != "windows":
            print("💡 If the 'codecraft' command doesn't work:")
            print("   1. Restart your terminal")
            print("   2. Or run: source ~/.bashrc (or ~/.zshrc)")
            print(f"   3. Or run directly: python3 {self.install_dir}/src/main.py")
            print()
        
        print("📖 For more information:")
        print("   https://github.com/CodeCraft/craft-code-cli")
        print()
    
    def run(self):
        """Run the installation process"""
        self.print_header()
        
        # Check Python
        if not self.check_python():
            if not self.install_python():
                print("\n❌ Installation failed: Could not install Python")
                return False
        
        # Install dependencies
        if not self.install_dependencies():
            print("\n❌ Installation failed: Could not install dependencies")
            return False
        
        # Create global command
        if not self.create_global_command():
            print("\n⚠️  Warning: Could not create global command")
            print("You can still run the application directly:")
            print(f"   python {self.install_dir}/src/main.py")
        
        # Test installation
        self.test_installation()
        
        # Print completion message
        self.print_completion()
        
        return True

def main():
    """Main entry point"""
    if len(sys.argv) > 1 and sys.argv[1] == "install":
        installer = CraftCodeInstaller()
        success = installer.run()
        sys.exit(0 if success else 1)
    else:
        print("Usage: python setup.py install")
        sys.exit(1)

if __name__ == "__main__":
    main()
